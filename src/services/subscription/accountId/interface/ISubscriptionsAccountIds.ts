export interface ISubscriptionsCollection {
    collection: ISubscriptionsAccountIds[];
}

export interface ISubscriptionsAccountIds {
    id: number;
    orderReference: string;
    status: string;
    catalogOfferCode: string;
    type: string;
    activatedAt: null;
    terminatedAt: null;
    createdAt: string;
    costCenter: null;
    tariffPlanCode: string;
    name: string;
    vip: boolean;
    coolOffAt: string;
    accountId: string;
    charges: ICharge[];
    discounts: IDiscount[];
    dependsOn: null;
    serviceGroup: string;
    acquisitionType: string;
    purchasedEquipments: unknown[];
    subscriberBirthDate: null;
    terminationStatus: boolean;
}

interface IDiscount {
    id: number;
    catalogCode: string;
    catalogItemType: string;
    activatedAt: null;
    terminatedAt: null;
    amountVatIncluded: number;
    amountVatExcluded: number;
    occurrence: null;
    billingType: string;
    manual: boolean;
    status: string;
}

interface ICharge {
    id: number;
    description: null | string;
    catalogCode: string;
    billingType: string;
    coolingOffPeriod: null;
    advanceArrears: string;
    pricePlanCatalogCode: string;
    overriddenPrice: null;
    activatedAt: null;
    terminatedAt: null;
    status: string;
    subscriptionId: number;
    serviceId: null;
    addonId: null;
    equipmentFinancingId: null;
}

export interface IServiceSubscription {
    collection: ICollection[];
}

interface ICollection {
    accountId: string;
    activatedAt: null;
    addressId: number;
    charges: unknown[];
    createdAt: string;
    discounts: unknown[];
    domain: string;
    dslUan: string;
    equipments: unknown[];
    id: number;
    name: string;
    networkElements: unknown[];
    networkId: null;
    status: string;
    subscriptionId: number;
    terminatedAt: null;
    updatedAt: string;
    usageQuotas: IUsageQuota[];
}

interface IUsageQuota {
    activatedAt: null;
    catalogCode: string;
    id: number;
    networkElements: INetworkElement[];
    paused: boolean;
    status: string;
    terminatedAt: null;
}

interface INetworkElement {
    activatedAt: null;
    catalogCode: string;
    id: number;
    status: string;
    terminatedAt: null;
}
