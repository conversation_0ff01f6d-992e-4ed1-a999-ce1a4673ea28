import { IFetchOptions } from "itsf-ui-common";
import { fetcher } from "@utils/fetcher";
import { IServiceSubscription, ISubscriptionsCollection } from "../interface/ISubscriptionsAccountIds";
import { getGatewayEndpoint, getMicroserviceEndpoint, InternalV1EndpointOptions } from "@api-routes/endpoints";
import { ISubscriptionsId, IAccountSubscription } from "../interface/ISubscriptionsForId";
import { IAddOns, ICollectionOffers, IDetailAddons, IGetServicesGroups } from "../interface/IAddOns";
import { IActiveServices } from "../interface/IActiveServices";
import { IAppointment } from "../interface/IAppointment";
// import { IOption } from "@common";
// import { IOption } from "@common";
// import { accountBalanceApiRouteGateway } from "@api-routes/accountReceivableFacade";
// import { IOption } from "@common";

export const UNIQUE_REFERENCE_SERVICE_ENDPOINT_PREFIX = "unique-references-service/";

export const UNIQUE_REFERENCE_SERVICE_ENDPOINT = getMicroserviceEndpoint(UNIQUE_REFERENCE_SERVICE_ENDPOINT_PREFIX, {
    ...InternalV1EndpointOptions,
    useApiGateway: true,
});

export const getServicesSubscriptions = (query: string, options: IFetchOptions = {}) =>
    fetcher<IServiceSubscription>(`${getGatewayEndpoint()}accounts/api/v1/services/subscriptionsIds/${query}`, {
        method: "GET",
        ...options,
    });

export const getSubscriptionsAccountIds = (query: string, options: IFetchOptions = {}) =>
    fetcher<ISubscriptionsCollection>(
        `${getGatewayEndpoint()}subscriptions/api/v1/subscriptions/account-ids/${query}`,
        {
            method: "GET",
            ...options,
        }
    );

export const getSubscriptionsTerminationStatus = (serviceId: string, options: IFetchOptions = {}) =>
    fetcher<[]>(`${getGatewayEndpoint()}/accounts/private/subscriptions/terminations/${serviceId}`, {
        method: "GET",
        ...options,
    });

export const getSubcriptionsId = (query: string) =>
    fetcher<ISubscriptionsId>(`${getGatewayEndpoint()}subscriptions/api/v1/subscriptions/${query}`, {
        method: "GET",
    });

export const getCatalogCharges = (query: string, options: IFetchOptions = {}) =>
    fetcher(`${getGatewayEndpoint(false)}catalog/api/v1/charges/${query}`, {
        method: "GET",
        ...options,
    });

export const getAddOnsServices = (query: string, options: IFetchOptions = {}) =>
    fetcher<IAddOns>(`${getGatewayEndpoint()}addons/api/v1/services/${query}/add-ons`, {
        method: "GET",
        ...options,
    });

// export const getOffersAddons = ({ channelCode, serviceId }: { channelCode: string; serviceId: string }) =>
//     fetcher<ICollectionOffers>(
//         `${getGatewayEndpoint()}addons/api/v1/private/auth/add-ons/channelCode/${channelCode}/serviceId/${serviceId}`,
//         {
//             method: "GET",
//         }
//     );

export const getOffersAddons = (channelCode: string, serviceId: string, options: IFetchOptions = {}) => {
    return fetcher<ICollectionOffers>(
        `${getGatewayEndpoint()}addons/api/v1/private/auth/add-ons/channelCode/${channelCode}/serviceId/${serviceId}`,
        {
            method: "GET",
            ...options,
        }
    );
};

export const getAddOnsForService = (serviceId: string, options: IFetchOptions = {}) => {
    return fetcher<IAddOns>(`${getGatewayEndpoint()}addons/api/v1/services/${serviceId}/add-ons`, {
        method: "GET",
        ...options,
    });
};

export const getActiveServices = (serviceId: string, options: IFetchOptions = {}) =>
    fetcher<IActiveServices>(`${getGatewayEndpoint()}accounts/api/v1/addons/serviceIds/${serviceId}/status/ACTIVE`, {
        method: "GET",
        ...options,
    });

export const getServicesGroups = (serviceGroup: string) =>
    fetcher<IGetServicesGroups>(`${getGatewayEndpoint()}catalog/api/v1/service-groups/${serviceGroup}`, {
        method: "GET",
    });

export const getServicesActives = (serviceId: string, options: IFetchOptions = {}) =>
    fetcher<IActiveServices>(`${getGatewayEndpoint()}accounts/api/v1/addons/serviceIds/${serviceId}/status/ACTIVE`, {
        method: "GET",
        ...options,
    });

export const getInformationAddons = (addonId: string) =>
    fetcher<IDetailAddons>(`${getGatewayEndpoint()}catalog/api/v1/add-ons/${`${addonId}_RETENTION`}`, {
        method: "GET",
    });

// export const getUniqueService = (options: IFetchOptions = {}) =>
//     fetcher(`${getGatewayEndpoint()}unique-references-service/api/v1/8`, {
//         method: "GET",
//         ...options,
//     });

export const getUniqueService = () => {
    const generateRandomReference = (length = 10): string => {
        const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        let result = "";
        for (let i = 0; i < length; i++) {
            const randomIndex = Math.floor(Math.random() * characters.length);
            result += characters.charAt(randomIndex);
        }

        return result;
    };

    return Promise.resolve({ reference: generateRandomReference() });
};

// export const getUniqueService = () => {
//     return fetcher(`${UNIQUE_REFERENCE_SERVICE_ENDPOINT}8`, {
//         method: "GET",
//     });
// };

// export const getUniqueService = ({ length = "8" }: { length?: string }) =>
//     fetcher<{ reference: string }>(
//         `${getGatewayEndpoint()}unique-references-service/api/v1/unique-references/${length}`,
//         {
//             method: "GET",
//         }
//     );

export const createAppointment = (data: IAppointment) => {
    return fetcher<string | number>(`${getGatewayEndpoint()}appointments/api/v1`, {
        method: "POST",
        body: { ...data },
    });
};

export const getAccountSubscription = (subcriptionId: string) =>
    fetcher<IAccountSubscription[]>(`${getGatewayEndpoint()}accounts/api/v1/subscriptionId/${subcriptionId}`, {
        method: "GET",
    });
