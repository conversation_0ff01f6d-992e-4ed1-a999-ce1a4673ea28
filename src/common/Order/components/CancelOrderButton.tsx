import { Button, ButtonProps, Tooltip } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useFeature } from "@hooks/useFeature";

interface IParams extends ButtonProps {
    cancelOrder?: () => void;
    isCancellable: boolean;
}

export const CancelOrderButton = (props: IParams) => {
    const { cancelOrder, isCancellable, ...rest } = props;
    const { t } = useTranslation(["order", "common"]);

    const canCancelOrder = useFeature(["CANCEL_ORDER"]);

    const button = (
        <Button variant="outlined" onClick={cancelOrder} {...rest}>
            {t("order:cancelOrder")}
        </Button>
    );

    if (!isCancellable || !canCancelOrder) {
        const tooltipTitle = !canCancelOrder ? t("common:notNecessaryPermissions") : t("order:orderNotCancellable");

        return (
            <Tooltip title={tooltipTitle}>
                <div data-testid="tooltip-disable-cancel-order-test">{button}</div>
            </Tooltip>
        );
    }

    return button;
};
