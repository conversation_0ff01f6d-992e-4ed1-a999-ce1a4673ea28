import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  Divider,
  SvgIcon,
  TableHead,
  TableBody,
  TableCell,
  TableRow,
  Typography,
  Box,
  Chip
} from "@mui/material";
import { centsToPrice, priceToCents } from "itsf-ui-common";
import { TContactDetails } from "../CustomerDetails/ICustomerDetails";
import { useViewAdjustments } from "./useViewAdjusments";
import { IElligibleInvoiceResponse } from "@modules/financial-document/interfaces/responses/ICreditNotesResponses";
import { IAdjustmentResponseDTO } from "@modules/adjustment/interfaces/payloads/IAdjustmentPayload";
import { useGetQueryParams } from "@hooks/useGetQueryParams";
import { useLocation } from "react-router-dom";
import { useSnackBar } from "@common/SnackBar";
import {
  SectionCard,
  SimpleTable,
  useOutlinedCardStyle,
  useSectionIconStyle,
  FormButton
} from "@common";
import { ReactComponent as BillIcon } from "@static/icons/bill.svg";
import OperationResultDialog from "@common/Dialog/AdjusmentsDialog/OperationResultDialog";
import { OperationConfirmDialog } from "@common/Dialog/AdjusmentsDialog/OperationConfirmDialog";
import { AdjustmentApprovalDialog } from "@common/Dialog/AdjusmentsDialog/AdjustmentApprovalDialog";



interface IPropsViewAdjusments {
  accountId: string;
  contactDetails: TContactDetails | undefined;
}

const ViewAdjusments = ({ accountId, contactDetails }: IPropsViewAdjusments) => {
  const { caseid: caseIdFromQuery } = useGetQueryParams("caseid");
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const caseIdFromLocation = searchParams.get("caseid");
  const { setSnackBarError } = useSnackBar();

  const { classes: outlinedCardClasses } = useOutlinedCardStyle();
  const { classes: iconClasses } = useSectionIconStyle();

  const statusColors: Record<string, "default" | "primary" | "success" | "error" | "warning" | "info"> = {
    init: "default",
    approved: "primary",
    denied: "error",
    dispute: "warning",
    completed: "success",
  };

  const {
    adjustmentsList,
    isLoadingAdjustments,
    loadAdjustmentsFromEligibleInvoices,
    eligibleInvoices,
    setCaseId,
    caseId,
    createCreditNoteFromAdjustment,
    updateAdjustmentStatus,
    caseVerified,
    setCaseVerified,
    canApprove,
    maxAcceptableAmountAutoApprovalCreditNoteCents,
    callAdjusmentCustomServiceGetAdjusment,
  } = useViewAdjustments({ accountId, contactDetails });

  const [showEligibleInvoices, setShowEligibleInvoices] = useState<boolean>(false);
  const [selectedInvoice, setSelectedInvoice] = useState<IElligibleInvoiceResponse | null>(null);
  const [creditNoteAmounts, setCreditNoteAmounts] = useState<{ [key: string]: number }>({});
  const [selectedAdjustment, setSelectedAdjustment] = useState<IAdjustmentResponseDTO | null>(null);
  const [isAuthorizingCreditNote, setIsAuthorizingCreditNote] = useState(false);
  const [isCreatingCreditNote, setIsCreatingCreditNote] = useState(false);
  const [isUpdatingStatusCreditNote, setIsUpdatingStatusCreditNote] = useState(false);

  //Variables para manejar la visualizacion del los dialogos
  const [isShowOperationResultDialog, setIsShowOperationResultDialog] = useState(false);
  const [isShowOperationConfirmDialog, setIsShowOperationConfirmDialog] = useState(false);
  const [isShowAdjustmentApprovalDialog, setIsShowAdjustmentApprovalDialog] = useState(false);
  const [isShowAdjustmentStatusChangeDialog, setIsShowAdjustmentStatusChangeDialog] = useState(false);
  const [isShowAdjustmentApprovalDialogPostCreation, setIsShowAdjustmentApprovalDialogPostCreation] = useState(false);
  
  const [descriptionChangeStatus, setDescriptionChangeStatus] = useState(String);
  const [currentIdAdjustment, setCurrentIdAdjustment] = useState<number>(0);


  useEffect(() => {
    const caseIdValue = caseIdFromLocation || caseIdFromQuery;
    const caseIdToUse = caseIdValue ? parseInt(caseIdValue, 10) : 0;
    setCaseId(caseIdToUse);
  }, [setCaseId, caseIdFromQuery, caseIdFromLocation]);

  const initAmountsFromInvoice = (invoice: IElligibleInvoiceResponse) => {
    const initialAmounts: { [key: string]: number } = {};
    invoice.items?.forEach((item) => {
      if (item.billing_section_id) initialAmounts[item.billing_section_id] = 0;
    });
    setCreditNoteAmounts(initialAmounts);
  };

  const handleCreateCreditNoteClick = () => {
    setSelectedAdjustment(null);
    setShowEligibleInvoices(true);
  };

  const handleInvoiceSelect = (invoice: IElligibleInvoiceResponse) => {
    setSelectedInvoice(invoice);
    initAmountsFromInvoice(invoice);
  };

  const handleInvoiceChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const value = e.target.value;
    const invoice = eligibleInvoices.find(inv => inv.invoice_number === value);
    setSelectedAdjustment(null);
    if (invoice) {
      handleInvoiceSelect(invoice);
    } else {
      setSelectedInvoice(null);
      setCreditNoteAmounts({});
    }
  };

  const handleAmountChange = (billingSeccionId: string, value: number, maxAmountInCents: number) => {
    const maxAmount = parseFloat(centsToPrice(maxAmountInCents));
    if (value >= 0 && value <= maxAmount) {
      setCreditNoteAmounts(prev => ({ ...prev, [billingSeccionId]: value }));
    }
  };

  const handleCreateCreditNote = async (): Promise<number> => {
    if (!selectedInvoice || !selectedInvoice.invoice_number) {
      return 0;
    }

    const details = Object.entries(creditNoteAmounts)
      .filter(([_, amount]) => amount > 0)
      .map(([billingSeccionId, amount]) => ({
        idRubro: billingSeccionId,
        amount: priceToCents(amount.toString()),
      }));

    if (details.length === 0) {
      setSnackBarError("Debe ingresar al menos un monto mayor a 0");
      return 0;
    }

    try {
      const success = await createCreditNoteFromAdjustment({
        invoiceId: parseInt(selectedInvoice.invoice_number, 10),
        details,
      });

      return success; 
    } catch (error) {
      console.error("Error creando Credit Note:", error);
      return 0;
    }
  };


  const handlePressCreateCreditNoteButtonTable = () => {
    if (!selectedInvoice || !selectedInvoice.invoice_number) return;

    const details = Object.entries(creditNoteAmounts)
      .filter(([_, amount]) => amount > 0)
      .map(([billingSeccionId, amount]) => ({
        idRubro: parseInt(billingSeccionId, 10),
        amount: priceToCents(amount.toString())
      }));

    if (details.length === 0) {
      setSnackBarError("Debe ingresar al menos un monto mayor a 0");
      return;
    }

    setIsShowOperationConfirmDialog(true);
  };

  const handleBackToList = () => {
    setSelectedInvoice(null);
    setSelectedAdjustment(null);
    setShowEligibleInvoices(false);
    setCreditNoteAmounts({});
  };

  const handleAuthorizeCreditNote = async (status: string) => {
    setIsUpdatingStatusCreditNote(true);
    if (!selectedAdjustment || !selectedAdjustment.id) {
      setSnackBarError("No hay adjustment seleccionado para autorizar");
      return;
    }

    setIsAuthorizingCreditNote(true);

    try {
      const success = await updateAdjustmentStatus({
        adjustmentId: selectedAdjustment.id,
        status: status as "approved" | "denied",
        adjustment: selectedAdjustment,
      });

      if (success) {
        setSelectedAdjustment(null);
        setShowEligibleInvoices(false);
        setSelectedInvoice(null);
        setCreditNoteAmounts({});
      }
    } catch (error) {
      console.error("Error autorizando credit note:", error);
    } finally {
      setIsAuthorizingCreditNote(false);
      setIsUpdatingStatusCreditNote(false);
      setIsShowAdjustmentApprovalDialogPostCreation(false)
    }
  };

  const handleViewDetailsFromAdjustment = (adj: IAdjustmentResponseDTO) => {
    const invoice = eligibleInvoices.find(inv => {
      const invNum = inv.invoice_number ? parseInt(inv.invoice_number, 10) : NaN;
      return Number.isFinite(invNum) && invNum === adj.invoiceId;
    });

    if (!invoice) {
      setSnackBarError(
        "No se encontraron los datos de la factura asociada a este ajuste."
      );
      return;
    }

    setShowEligibleInvoices(true);
    setCaseId(adj.caseId ?? 0);
    setSelectedAdjustment(adj);
    setSelectedInvoice(invoice);

    const initial: { [key: string]: number } = {};
    invoice.items?.forEach((item) => {
      if (item.billing_section_id) initial[item.billing_section_id] = 0;
    });

    adj.detail?.forEach(d => {
      initial[String(d.idRubro)] = parseFloat(centsToPrice(d.amount ?? 0));
    });

    setCreditNoteAmounts(initial);
  };

  const showAuthorizeButton =
    !!selectedAdjustment &&
    !["approved","denied" ,"completed"].includes(
      (selectedAdjustment.detail?.[0]?.status || "").toLowerCase()
    );


  //Este el metodo que se ejecuta cuando se presiona el boton aceptar en el dialogo de confirmacion de creacion de la request de la credit note
  const handlePressConfirmCreateButton = async () => {

    //Pregunto si el caso esta verificado
    if (caseVerified) {
      setIsCreatingCreditNote(true);
      try {
        const idAdjustment = await handleCreateCreditNote();

        if (idAdjustment === 0) {
          setSnackBarError("Error creating credit note 1");
          return;
        }

        console.log("idAdjustment",idAdjustment);
        console.log("adjustmentsList",adjustmentsList);

        // Busco el adjustment recien creado en la lista de adjustments
        const currentAdjusment = adjustmentsList.find(adjustment => adjustment.id === idAdjustment);

        //Guardo al id del adjustment recien creado para cambiarle el status si se tiene privilegios para aprobar o no
        setCurrentIdAdjustment(idAdjustment);


        console.log("currentAdjusment",currentAdjusment);
        console.log("currentAdjusment.totalAmount",currentAdjusment?.totalAmount);

        // Si no se encuentra el adjustment en la lista, intentar buscarlo directamente
        let finalAdjustment = currentAdjusment;
        if(!currentAdjusment) {
          console.log("Adjustment not found in list, trying direct search...");
          try {
            const directSearchResult = await callAdjusmentCustomServiceGetAdjusment({
              adjustmentId: idAdjustment
            });
            if (directSearchResult) {
              finalAdjustment = directSearchResult;
              console.log("Found adjustment via direct search:", finalAdjustment);
            } else {
              setSnackBarError("Error finding created adjustment");
              setIsShowOperationConfirmDialog(false);
              return;
            }
          } catch (error) {
            console.error("Error in direct search:", error);
            setSnackBarError("Error finding created adjustment");
            setIsShowOperationConfirmDialog(false);
            return;
          }
        }

        if(!finalAdjustment || !finalAdjustment.totalAmount){
          setSnackBarError("Error: adjustment created but missing total amount");
          setIsShowOperationConfirmDialog(false);
          return;
        }



        //Las siguientes condiciones son para cambiar el status de la request de la credit note (adjusment)
        //Pregunto si el monto es mayor al maximo aceptable para auto-aprobar
        console.log("MAXIMO ACEPTABLE",maxAcceptableAmountAutoApprovalCreditNoteCents);
        console.log("MONTO",finalAdjustment.totalAmount);
        if(maxAcceptableAmountAutoApprovalCreditNoteCents < finalAdjustment.totalAmount){
          
          //Pregunto si el usuario tiene permisos para aprobar
          if(canApprove){
            //Mostrar dialogo para confirmar la accion de autorizar la nota de crédito
            setIsShowOperationConfirmDialog(false);
            setIsShowAdjustmentApprovalDialog(true);    
            console.log("SI PUEDE APROBAR");

          }else{
            //No tiene permisos para aprobar se muestre un snackbar indicando que no tiene permisos para aprobar
            
            //Hacer un patch al adjustment para colocarlo como dispute 
            const success = await updateAdjustmentStatus({
                adjustmentId: idAdjustment,
                status: "dispute",
                adjustment: finalAdjustment,
            });

            setIsShowOperationConfirmDialog(false);
            if(success){
              setSnackBarError("You do not have permission to approve credit notes");            
            }
            

          }

        }else{
          //El monto total de la credit note es menor al maximo aceptable, por lo tanto, se aprueba automaticamente
          // 1. Hacer un patch al adjustment para aprobarlo automaticamente
          
          
          const success = await updateAdjustmentStatus({
            adjustmentId: idAdjustment,
            status: "approved",
            adjustment: finalAdjustment,
          });

          // 2. Mostrar dialogo de final indicando que la operacion fue exitosa
          setIsShowOperationConfirmDialog(false);
          
          if(!success){
            setSnackBarError("Error creating credit note 3");
            return;
          }else{
              setIsShowOperationResultDialog(true);
          }       

        }

        //Resetear las variables
        if (idAdjustment) {
          setSelectedInvoice(null);
          setCreditNoteAmounts({});
          setSelectedAdjustment(null);
          setShowEligibleInvoices(false);
        }
        setCaseVerified(false);
      } catch (error) {
        console.error("Error in handlePressConfirmCreateButton:", error);
        setSnackBarError("Error creating credit note");
      } finally {
        setIsCreatingCreditNote(false);
      }
    } else {
      setSnackBarError ("Please verify the case before creating a credit note");
    }

  };


  const handlePressApproveDeniedButton = async () => {
    if(canApprove){
      //Mostrar dialogo para confirmar la accion de autorizar la nota de crédito
      setIsShowAdjustmentApprovalDialogPostCreation(true);    
      console.log("SI PUEDE APROBAR");

    }else{      
      setSnackBarError("You do not have permission to approve credit notes");
    }
  };






  const handlePressChangeStatusButton = async (status : string) => {
    let success = false;
    setIsUpdatingStatusCreditNote(true);
    try {
      const directSearchResult = await callAdjusmentCustomServiceGetAdjusment({
        adjustmentId: currentIdAdjustment
      });
      if (directSearchResult) {
        console.log("Found adjustment via direct search:", directSearchResult);
            switch (status) {
              case "approved":
                success = await updateAdjustmentStatus({
                  adjustmentId: currentIdAdjustment,
                  status: "approved",
                  adjustment: directSearchResult,
                });
                setDescriptionChangeStatus("Adjustment approved");
                break;
              case "denied":
                success = await updateAdjustmentStatus({
                  adjustmentId: currentIdAdjustment,
                  status: "denied",
                  adjustment: directSearchResult,
                });
                setDescriptionChangeStatus("Adjustment denied");      
                break;
              case "dispute":
                  setIsShowAdjustmentApprovalDialog(false);
                  success = await updateAdjustmentStatus({
                    adjustmentId: currentIdAdjustment,
                    status: "dispute",
                    adjustment: directSearchResult,
                  });
                  break;
              default:
                setIsShowAdjustmentApprovalDialog(false);
                break;
            }
            setIsShowAdjustmentApprovalDialog(false);
                
            if(success){
              if(status === "approved" || status === "denied"){
                setIsShowAdjustmentStatusChangeDialog(true);
              }              
            }else{
              setSnackBarError("Error changing credit note status ");
            }  
            setIsUpdatingStatusCreditNote(false);
      } else {
        setSnackBarError("Error finding created adjustment");
        setIsShowAdjustmentApprovalDialog(false);
        setIsUpdatingStatusCreditNote(false);
      }
    } catch (error) {
      console.error("Error in direct search:", error);
      setIsShowAdjustmentApprovalDialog(false);
      setIsUpdatingStatusCreditNote(false);
    }
  };

    

  return (
    <Box >
    <Card className={outlinedCardClasses.main} elevation={0} variant="outlined">
      <CardContent>
        <SectionCard
          icon={<SvgIcon className={iconClasses.main} component={BillIcon} viewBox="0 0 21 19" />}
          title={showEligibleInvoices ? "Create Credit Note" : "Adjustments List"}
          actionButtons={
            <Box display="flex" gap={1}>
              {!showEligibleInvoices && (
                <FormButton
                  buttonText="Create Credit Note"
                  variant="contained"
                  color="primary"
                  onClick={handleCreateCreditNoteClick}
                />
              )}

              {!showEligibleInvoices && (
                <FormButton
                  buttonText={isLoadingAdjustments ? "Loading..." : "Refresh"}
                  variant="outlined"
                  color="primary"
                  disabled={isLoadingAdjustments}
                  onClick={loadAdjustmentsFromEligibleInvoices}
                />
              )}

              {showEligibleInvoices && (
                <FormButton
                  buttonText="Back"
                  variant="outlined"
                  color="secondary"
                  onClick={handleBackToList}
                />
              )}
            </Box>
          }
        >
          {showEligibleInvoices && !isLoadingAdjustments && (
            <Grid container direction="row" spacing={2} width="100%">
              <Grid item xs={12}>
                <Divider textAlign="center">Case Number</Divider>
              </Grid>
              <Grid item xs={4}>
                <TextField
                  fullWidth
                  label="Case Number"
                  type="number"
                  value={Number.isFinite(caseId) ? caseId : 0}
                  onChange={(e) => {
                    const v = parseInt(e.target.value, 10);
                    setCaseId(Number.isFinite(v) ? v : 0);
                  }}
                />
              </Grid>
              <Grid item xs={2}>
                <Button
                  color="primary"
                  variant="outlined"
                  onClick={() => {
                    // Verificar lógica aquí si es necesaria
                    setCaseVerified(true);
                  }}
                >
                  Verify
                </Button>
              </Grid>
              <Grid item xs={12}>
                <Divider textAlign="center">Invoice Details</Divider>
              </Grid>
              <Grid item xs={4}>
                <TextField
                  fullWidth
                  select
                  label="Invoice"
                  value={selectedInvoice?.invoice_number ?? ""}
                  onChange={handleInvoiceChange}
                  SelectProps={{
                    native: true,
                  }}
                >
                  <option value="" disabled>
                    {eligibleInvoices.length === 0
                      ? "No eligible invoices"
                      : ""}
                  </option>
                  {eligibleInvoices.map((inv, idx) => (
                    <option key={`${inv.invoice_number}-${idx}`} value={inv.invoice_number ?? ""}>
                      {inv.invoice_number} ({inv.items?.length ?? 0} items)
                    </option>
                  ))}
                </TextField>
              </Grid>
            </Grid>
          )}

          {selectedInvoice && (
            <Box mt={2}>
              <SimpleTable cellTextColor="black">
                <TableHead>
                  <TableRow>
                    <TableCell>Billing Section ID</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Available Amount</TableCell>
                    <TableCell>Credit Note Amount</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {selectedInvoice.items?.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>{item.billing_section_id}</TableCell>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>
                        ${centsToPrice(item.net_amount_left || 0)}
                      </TableCell>
                      <TableCell>
                        <TextField
                          type="number"
                          size="small"
                          inputProps={{
                            min: 0,
                            max: parseFloat(centsToPrice(item.net_amount_left || 0)),
                            step: 0.01
                          }}
                          value={creditNoteAmounts[item.billing_section_id || ""] || 0}
                          onChange={(e) => {
                            const value = parseFloat(e.target.value) || 0;
                            handleAmountChange(
                              item.billing_section_id || "",
                              value,
                              item.net_amount_left || 0
                            );
                          }}
                          sx={{ width: 150 }}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </SimpleTable>

              <Box display="flex" justifyContent="flex-end" gap={2} mt={2}>
                {showAuthorizeButton && (
                  <FormButton
                    buttonText={isAuthorizingCreditNote ? "Authorizing..." : "Authorize Credit Note"}
                    variant="contained"
                    color="primary"
                    disabled={isAuthorizingCreditNote}
                    onClick={handlePressApproveDeniedButton}//handleAuthorizeCreditNote}
                  />
                )}
                {!selectedAdjustment && !showAuthorizeButton && (
                  <FormButton
                    buttonText="Create Credit Note"
                    variant="contained"
                    color="primary"
                    onClick={handlePressCreateCreditNoteButtonTable}
                  />
                )}
              </Box>
            </Box>
          )}

          {!showEligibleInvoices && !isLoadingAdjustments && (
            <SimpleTable cellTextColor="black">
              <TableHead>
                <TableRow>
                  <TableCell>Adjustment</TableCell>
                  <TableCell>Invoice</TableCell>
                  <TableCell>Case</TableCell>
                  <TableCell>Total Amount</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Creation Date</TableCell>
                  <TableCell>Created By</TableCell>
                  <TableCell>Details</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {adjustmentsList.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} align="center">
                      <Typography color="textSecondary">
                        No adjustments found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  adjustmentsList.map((adjustment) => (
                    <TableRow key={adjustment.id}>
                      <TableCell>{adjustment.id}</TableCell>
                      <TableCell>{adjustment.invoiceId}</TableCell>
                      <TableCell>{adjustment.caseId}</TableCell>
                      <TableCell>
                        ${centsToPrice(adjustment.totalAmount || 0)}
                      </TableCell>
                      <TableCell>{adjustment.type || ""}</TableCell>
                      <TableCell>
                          {adjustment.detail?.[0]?.status ? (
                            <Chip
                              label={adjustment.detail[0].status}
                              color={statusColors[adjustment.detail[0].status.toLowerCase()] || "default"}
                              size="small"
                            />
                          ) : (
                            ""
                          )}
                      </TableCell>
                      <TableCell>{adjustment.createdDate?.split("T")[0] || ""}</TableCell>
                      <TableCell>{adjustment.createdBy || "N/A"}</TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          variant="outlined"
                          color="primary"
                          onClick={() => handleViewDetailsFromAdjustment(adjustment)}
                        >
                          Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </SimpleTable>
          )}
        </SectionCard>
      </CardContent>
    </Card>

      {/*DIALOGO PARA INDICAR QUE LA OPERACION DE LA REQUEST DE LA CREDIT NOTE FUE EXITOSA*/}
      <OperationResultDialog
        isOpen={isShowOperationResultDialog}
        onClose={() => setIsShowOperationResultDialog(false)} //REFRESCAR EL COMPONENTE
        message="Credit note created successfully"        
      />

      {/*DIALOGO PARA PREGUNTAR AL USUARIO SI DESEA CREAR LA REQUEST DE LA CREDIT NOTE*/}
      <OperationConfirmDialog
        isOpen={isShowOperationConfirmDialog}
        handleCancel={() => setIsShowOperationConfirmDialog(false)}
        handleConfirmOperations={handlePressConfirmCreateButton}
        title="Create Credit Note"
        message="Are you sure you want to create the credit note?"
        isLoading={isCreatingCreditNote}
        confirmButtonText={isCreatingCreditNote ? "Creating..." : "Confirm"}
      />

      {/*DIALOGO PARA PREGUNTAR AL USUARIO SI DESEA APROBAR O DENEGAR LA REQUEST DE LA CREDIT NOTE (CUANDO SE CREA LA CREDIT NOTE)*/}
      <AdjustmentApprovalDialog
        isOpen={isShowAdjustmentApprovalDialog}
        handleCancel={() => handlePressChangeStatusButton("dispute")}
        handleClose={() => handlePressChangeStatusButton("dispute")}
        handleApproveOperations={()=> handlePressChangeStatusButton("approved")}
        handleDeniedOperations={()=> handlePressChangeStatusButton("denied")}
        title="Adjustment Approval"
        message="Are you sure you want to approve the adjustment?"
        isLoading={isUpdatingStatusCreditNote}
        approveButtonText="Approve"
        deniedButtonText="Deny"
      /> 


      {/*DIALOGO PARA INDICAR QUE SE CAMBIO EL ESTATUS AL ADJUMENT A APPROVE O DENIED*/}
      <OperationResultDialog
        isOpen={isShowAdjustmentStatusChangeDialog}
        onClose={() => setIsShowAdjustmentStatusChangeDialog(false)} 
        message={descriptionChangeStatus}        
      />


      {/*DIALOGO PARA PREGUNTAR AL USUARIO SI DESEA APROBAR O DENEGAR LA REQUEST DE LA CREDIT NOTE (CUANDO SE AUTORIZA POSTERIOR A LA CREACION CUANDO EL USER ACTUAL NO TIENE PERMISOS)*/}
      <AdjustmentApprovalDialog
        isOpen={isShowAdjustmentApprovalDialogPostCreation}
        handleCancel={() => setIsShowAdjustmentApprovalDialogPostCreation(false)}
        handleClose={() => setIsShowAdjustmentApprovalDialogPostCreation(false)}
        handleApproveOperations={()=> handleAuthorizeCreditNote("approved")}
        handleDeniedOperations={()=> handleAuthorizeCreditNote("denied")}
        title="Adjustment Approval"
        message="Are you sure you want to approve the adjustment?"
        isLoading={isUpdatingStatusCreditNote}
        approveButtonText="Approve"
        deniedButtonText="Deny"
      /> 



    </Box>


  );
};

export default ViewAdjusments;