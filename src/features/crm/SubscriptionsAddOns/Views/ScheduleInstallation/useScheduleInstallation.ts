import { useFetchState } from "@hooks/useFetchState";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { getUniqueService } from "@services/subscription/accountId/api/getSubscriptionsAccountIds";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
// eslint-disable-next-line import/no-duplicates
import { format, parseISO } from "date-fns";
import { APPOINTMENT_DATE_FORMAT } from "@constants";
// eslint-disable-next-line import/no-duplicates
import { es } from "date-fns/locale";
import { formatInTimeZone } from "date-fns-tz";

export const useScheduleInstallation = () => {
    const { endFetchingError } = useFetchState();

    const [isSuccessCallId, setIsSuccessCallId] = useState<boolean>(false);
    const [callId, setCallId] = useState<string>("");
    const [availableSlots, setAvailableSlots] = useState<ISlot[]>();
    const [selectedSlot, setSelectedSlot] = useState<ISlot>();
    const { t } = useTranslation(["customer", "appointment"]);

    const uniqueService = async () => {
        try {
            const response = await getUniqueService();
            setCallId(response.reference);
            setIsSuccessCallId(true);
        } catch (error) {
            endFetchingError(error);
        }
    };

    const procesarSlot = (slot: ISlot) => {
        const titulo = format(parseISO(slot.start), APPOINTMENT_DATE_FORMAT, { locale: es });
        const startHour = parseISO(slot.start).getUTCHours();
        const horaInicio = formatInTimeZone(slot.start,'UTC','h:mm',{ locale: es });
        const horaFin = formatInTimeZone(slot.finish,'UTC','h:mm',{ locale: es });
        
        const time_slot = startHour === 8 ? "AM" : "PM";
        const jornada = startHour === 8 ? t("appointment:morning") : t("appointment:afternoon");
        const horario = horaInicio + " - " + horaFin;

        return {
            titulo,
            jornada,
            horario,
            time_slot,
        };
    };

    useEffect(() => {
        const uniqueCallId = localStorage.getItem("UNIQUE_CALL_ID");
        if (!isSuccessCallId && !uniqueCallId) {
            uniqueService();
        }

        if (uniqueCallId) {
            setCallId(uniqueCallId);
        }
    }, [isSuccessCallId]);

    useEffect(() => {
        if (callId) {
            localStorage.setItem("UNIQUE_CALL_ID", callId);
        }
    }, [callId]);

    return {
        t,
        callId,
        setAvailableSlots,
        availableSlots,
        procesarSlot,
        selectedSlot,
        setSelectedSlot,
    };
};
