import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { useSnackBar } from "@common";
import { useFetchState } from "@hooks/useFetchState";
import { Divider, Grid, TextField, Typography } from "@mui/material";
import { useScheduleInstallation } from "./useScheduleInstallation";
import { ScheduleSlotsForm } from "@features/acquisition/PostpayAcquisition/Postpay/common/ScheduleInstallationStep/ScheduleSlotsForm/ScheduleSlotsForm";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { CONTACT_ADDRES_TYPE_BILLING, CONTACT_ADDRES_TYPE_INSTALLATION, MCCRM_COMMENT } from "@constants";
import SlotsForm from "./SlotsForm/SlotsForm";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { ConfirmationModal } from "@common";
import { processTaskEx } from "@modules/tigoSalesFacade/apis/v1/field-service";
import { FieldServiceConfirmAppointment } from "@modules/tigoSalesFacade/interfaces/payloads/ITigoFieldServiceConfirmAppointment";
import { useTranslation } from "react-i18next";
import ScheduleConfirmationModal from "@common/styleComponents/OrderConfirmationModal/ScheduleConfirmationModal";
import { FieldServiceConfirmAppointmentWFE } from "@modules/tigoSalesFacade/interfaces/payloads/ITigoFieldServiceConfirmAppointmentWFE";
import { processTasKWFE } from "@modules/tigoSalesFacade/apis/v1/confirmAppointment";
import { useStyle } from "../../style";

interface IPropsScheduleInstallation {
    contact: TContactDetails | undefined;
    setSuccessScheduleInstallation: Dispatch<SetStateAction<boolean>>;
    setScheduleInstallation: Dispatch<SetStateAction<ISlot>>;
    setCorrelationId: Dispatch<SetStateAction<string>>;
    scheduleSuccess?: boolean;
    onBackToAddNewOffer?: () => void;
    callIdFromOrder?: string; 
    componentScheduleTitle? : string;
}

const TASK_TYPE_ADDONS = process.env.TASK_TYPE_ADDONS || "";
const TASK_CATEGORY_ADDONS = process.env.TASK_CATEGORY_ADDONS || "";
const ENVIRONMENT_TIME_INTERVAL_NUMBER = process.env.ENVIRONMENT_TIME_INTERVAL_NUMBER || "";
const ENVIRONMENT_TIME_INTERVAL_AREA = process.env.ENVIRONMENT_TIME_INTERVAL_AREA || "";
const ENVIRONMENT_TIME_INTERVAL_DISTRICT = process.env.ENVIRONMENT_TIME_INTERVAL_DISTRICT || "";
const ENVIRONMENT_TIME_INTERVAL_PRIORITY = process.env.ENVIRONMENT_TIME_INTERVAL_PRIORITY || "";
const ENVIRONMENT_TIME_INTERVAL_COUNTRYID = process.env.ENVIRONMENT_TIME_INTERVAL_COUNTRYID || "";
const ENVIRONMENT_TIME_INTERVAL_CITY = process.env.ENVIRONMENT_TIME_INTERVAL_CITY || "";


const ScheduleInstallation = ({
    contact,
    setSuccessScheduleInstallation,
    setScheduleInstallation,
    setCorrelationId,
    scheduleSuccess = false,
    onBackToAddNewOffer,
    callIdFromOrder,
    componentScheduleTitle,
}: IPropsScheduleInstallation) => {
    const { t } = useTranslation(["acquisition", "appointment", "address"]);
    const { callId: callIdHook, setAvailableSlots, availableSlots, selectedSlot, setSelectedSlot, procesarSlot } =
        useScheduleInstallation();

    const { setSnackBarSuccess, setSnackBarError } = useSnackBar();
    const { startFetching, endFetching, endFetchingError } = useFetchState();

    const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
    const [tempSlotInfo, setTempSlotInfo] = useState<ReturnType<typeof procesarSlot> | null>(null);
    const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);

    const filterContact = contact?.contactAddresses.find((address) => address.type === CONTACT_ADDRES_TYPE_BILLING);
    const filterAddress = contact?.contactAddresses.find((address) => address.type === CONTACT_ADDRES_TYPE_INSTALLATION);

    const infoContact = contact?.person;
    const mainEmail = contact?.emails.find((email) => email.type === "MAIN")?.email ?? "";

    const callId = callIdFromOrder || callIdHook;
     const [latitude, longitude] = (filterAddress?.streetNumber ?? "").split("**");
    
    const { classes } = useStyle();

    useEffect(() => {
        if (selectedSlot) {
            setSuccessScheduleInstallation(true);
            setScheduleInstallation(selectedSlot);
        }
        if (callId) {
            setCorrelationId(callId);
        }
    }, [selectedSlot, callId]);

    const handleCloseConfirmModal = () => {
        setTempSlotInfo(null);
        setIsConfirmModalOpen(false);
    };

    const handleBackHome = () => {
        if (onBackToAddNewOffer) {
            onBackToAddNewOffer();
        } else {
            window.location.href = "/";
        }
    };

    const handleConfirmSlot = async () => {
        if (!selectedSlot || !contact) {
            return;
        }

        setIsConfirmModalOpen(false);
        try {
            startFetching();

            const payload: FieldServiceConfirmAppointment = {
                callId: callId, 
                earlyStart: selectedSlot.start,
                lateStart: selectedSlot.finish,
                appointmentStart: selectedSlot.start,
                appointmentFinish: selectedSlot.finish,
                district: filterAddress?.poBox ?? ENVIRONMENT_TIME_INTERVAL_DISTRICT,
                street: filterContact?.addressLine1 ?? "",
                city: ENVIRONMENT_TIME_INTERVAL_CITY,
                area: ENVIRONMENT_TIME_INTERVAL_AREA,
                taskType: TASK_TYPE_ADDONS,
                taskTypeCategory: TASK_CATEGORY_ADDONS,
                mcContactEmail: mainEmail,
                dueDate: selectedSlot?.finish,
                countryId: ENVIRONMENT_TIME_INTERVAL_COUNTRYID,
                number: Number(ENVIRONMENT_TIME_INTERVAL_NUMBER),
                priority: Number(ENVIRONMENT_TIME_INTERVAL_PRIORITY),
                mcBillingAccountInfo: contact?.contactAddresses?.[0]?.addressLine1 ?? "",
                mcCustomerCode: contact?.identityDocuments?.[0]?.identifier ?? "",
                mcCustomerPhoneNumber: contact?.phoneNumbers?.[0]?.phoneNumber
                    ? Number(contact.phoneNumbers[0].phoneNumber)
                    : undefined,
                latitude: latitude || "",
                longitude: longitude || "",
                mcCustomerIdentityNumber: contact?.identityDocuments?.[0]?.identifier ?? "",
                mcCustomerClass: "Residencial",
                mcOpeningReason: "",
                customer: `[${contact?.identityDocuments ?? ""}] ${contact?.person?.firstName ?? ""} ${contact?.person?.lastName ?? ""}`,
                tdRequired: true,
                mcCrmComment: MCCRM_COMMENT,
                mcConnectionData: (filterContact?.addressLine1 ?? "") + (filterContact?.street ?? ""),
                contactName: contact?.person?.firstName ?? "",
                contactPhoneNumber: contact?.phoneNumbers?.[0]?.phoneNumber
                    ? Number(contact.phoneNumbers[0].phoneNumber)
                    : undefined,
            };

            const response = await processTaskEx(payload);

            if (response?.messageError && response.messageError !== "") {
                setSnackBarError(`DENEGADO: ${response.messageError}`);
                endFetching();
                return;
            }

            const payloadWFE: FieldServiceConfirmAppointmentWFE = {
                addressId: filterContact?.id?.toString() ?? "",
                at: selectedSlot.start,
                contactUuid: contact.contactUuid ?? "",
                correlationId: callId ? Number(callId) : Number(new Date().getTime()), 
                duration: 30,
                reasonCode: "Equipo dañado",
                source: "TIGO_WEB",
                timeSlotCode: procesarSlot(selectedSlot).time_slot
            };

            await processTasKWFE(callId, payloadWFE);

            setSuccessScheduleInstallation(true);
            setScheduleInstallation(selectedSlot);
            setSnackBarSuccess(t("appointment:confirmSuccess"));
            setIsSuccessModalOpen(true);
            endFetching();
        } catch (error: any) {
            endFetchingError(error);
            setSnackBarError(`${t("appointment:confirmError")}: ${error.message}`);
        }
    };

    const handleSlotSelection = (slot: ISlot) => {
        const parsedSlot = procesarSlot(slot);
        setTempSlotInfo(parsedSlot);
        setSelectedSlot(slot);
        setIsConfirmModalOpen(true);
    }

    return (
        <>
            <Grid container direction={"row"} spacing={2}>
                {componentScheduleTitle && (
                    <Grid item xs={12}>
                        <Typography
                            className={`${classes.titleAddOn} ${classes.subscriptionTitleContainer}`} sx={{ pb: 1 }}
                        >
                        {componentScheduleTitle}
                        </Typography>
                    </Grid>
                )}
                <Grid item xs={12}>
                    <Divider textAlign="center">{t("appointment:installationAddress")}</Divider>
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField
                        disabled
                        fullWidth
                        label={t("appointment:province")}
                        value={filterContact?.addressLine2 ?? "provincia"}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField disabled fullWidth label={t("appointment:district")} value={filterContact?.town ?? "distrito"} />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField
                        disabled
                        fullWidth
                        label={t("appointment:township")}
                        value={filterContact?.addressLine1 ?? "township"}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField
                        disabled
                        fullWidth
                        label={t("appointment:neighborhood")}
                        value={filterContact?.area ?? "neighborhood"}
                    />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField disabled fullWidth label={t("appointment:street")} value={filterContact?.street ?? "street"} />
                </Grid>
                <Grid item md={6} xs={12}>
                    <TextField
                        disabled
                        fullWidth
                        label={t("appointment:house")}
                        value={filterContact?.addressLine3 ?? "house"}
                    />
                </Grid>
                <Grid item xs={12}>
                    <Divider textAlign="center">{t("appointment:checkAvailability")}</Divider>
                </Grid>
                <Grid item xs={12}>
                    <ScheduleSlotsForm
                        address={filterContact?.addressLine1 ?? ""}
                        callId={callId}
                        district={filterAddress?.poBox ?? ENVIRONMENT_TIME_INTERVAL_DISTRICT}
                        latitude={latitude ?? ""}
                        longitude={longitude ?? ""}
                        setAvailableSlots={setAvailableSlots}
                        taskType={TASK_TYPE_ADDONS}
                        taskTypeCategory={TASK_CATEGORY_ADDONS}
                    />
                </Grid>
                {availableSlots && (
                    <div>
                        {scheduleSuccess !== true && (
                            <>
                            <br />
                            <br />
                                <Grid item xs={12}>
                                    <Divider textAlign="center">{t("appointment:availableAppointmentSlots")}</Divider>
                                </Grid>
                                <Grid item xs={12}>
                                    {/* Mostrar todos los slots disponibles (AM y PM) */}
                                    <SlotsForm 
                                        availableSlots={availableSlots} 
                                        setSelectedSlot={handleSlotSelection} 
                                    />
                                </Grid>
                            </>
                        )}
                    </div>
                )}
            </Grid>
            { }
            <ConfirmationModal
                open={isConfirmModalOpen}
                title={t("appointment:confirmTitle")}
                handleClose={handleCloseConfirmModal}
                handleConfirm={handleConfirmSlot}
            >
                <p>{t("appointment:confirmMessage")}</p>
                {tempSlotInfo && (
                    <Grid container marginTop={2} spacing={2}>
                        <Grid item md={6} xs={12}>
                            <TextField
                                disabled
                                fullWidth
                                label={t("appointment:detailAppointment.date")}
                                value={tempSlotInfo.titulo}
                            />
                        </Grid>
                        <Grid item md={6} xs={12}>
                            <TextField disabled fullWidth label={tempSlotInfo.jornada} value={tempSlotInfo.horario} />
                        </Grid>
                    </Grid>
                )}
            </ConfirmationModal>
            <ScheduleConfirmationModal
                isOpen={isSuccessModalOpen}
                message={t("appointment:confirmSuccess")}
                onClose={() => setIsSuccessModalOpen(false)}
                onBackHome={handleBackHome}
            />
        </>
    );
};

export default ScheduleInstallation;
