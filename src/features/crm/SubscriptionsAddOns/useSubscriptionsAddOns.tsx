/* eslint-disable no-console */
import { useFetchState } from "@hooks/useFetchState";
import { debounce, SelectChangeEvent } from "@mui/material";
import {
    getSubscriptionsAccountIds,
    getSubcriptionsId,
    getCatalogCharges,
    createAppointment,
    getServicesSubscriptions,
    getUniqueService,
    getAddOnsForService,
    getSubscriptionsTerminationStatus,
} from "@services/subscription/accountId/api/getSubscriptionsAccountIds";
import {
    ADDONS_INTERNET,
    ADDONS_TV,
    ADDONS_MATERIAL,
    REASON_CODE_INSTALL,
    SOURCE_CODE_UI,
    STATUS_ADD_ONS,
    TIME_SLOT_CODE_AM,
    TIME_SLOT_CODE_PM,
} from "@constants";
import { IAddOns } from "@services/subscription/accountId/interface/IAddOns";
import { ICatalogCharges } from "@services/subscription/accountId/interface/ICatalogs";
import {
    IServiceSubscription,
    ISubscriptionsCollection,
} from "@services/subscription/accountId/interface/ISubscriptionsAccountIds";
import { ISubscriptionsId } from "@services/subscription/accountId/interface/ISubscriptionsForId";
import { IError } from "itsf-ui-common";
import { useCallback, useEffect, useRef, useState } from "react";
import { TContactDetails } from "../CustomerDetails/ICustomerDetails";
import { postTigoSalesRiskManagement } from "@modules/tigoSalesFacade/apis/v1/risk-management";
import { ERiskTransactionType } from "@modules/tigoSalesFacade/interfaces/payloads/ITigoSalesRiskManagementPayload";
import {
    ERiskCodeType,
    ITigoRiskManagementResponse,
} from "@modules/tigoSalesFacade/interfaces/responses/ITigoRiskManagementResponse";
import { useTranslation } from "react-i18next";
import { useStyle } from "./style";
import { IRenderComponentStep } from "@features/acquisition/PostpayAcquisition/Postpay/common/CustomerInformationStep/ICustomerInformationStep";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { format } from "date-fns";
import { ITigoCbsAccountsResponse } from "@modules/tigoSalesFacade/interfaces/responses/ITigoCbsAccountsResponse";
import { removeAddonActionApiRoute } from "@api-routes/addon";
import { IKeycloackChannel, useChannel } from "@common";
import { fetcher } from "@utils/fetcher";
import { ITigoSalesProspectLead } from "@modules/tigoSalesFacade/interfaces/models/ITigoSalesProspectLead";
import { EAddressType } from "../CustomerDetails/Address/IAddress";
import { IContact } from "@common/Contact/IContact";
import { IAddress } from "@common/Address/IAddress";
import { v4 as uuidv4 } from "uuid";
import dayjs from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import { useAddonStore } from "./useAddonStore";
import { getConfig } from "@config";
import { getGatewayEndpoint } from "@api-routes/endpoints";

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

enum Colors {
    green = "#17C653",
    red = "#F8285A",
    orange = "#F6C000",
}

interface IUseSubscriptionsAddOns {
    contactDetails: TContactDetails | undefined;
    accountsCbs: ITigoCbsAccountsResponse | undefined;
    otpEmailSuccess: boolean;
    otpSmsSuccess: boolean;
}

export const useSubscriptionsAddOns = ({
    contactDetails,
    accountsCbs,
    otpEmailSuccess,
    otpSmsSuccess,
}: IUseSubscriptionsAddOns) => {
    const { startFetching, endFetching, isLoading } = useFetchState();
    const { channel } = useChannel();
    const { classes } = useStyle();

    const [correlationId, setCorrelationId] = useState<string | undefined>();
    const [error, setError] = useState<IError | undefined>();
    const [isOpen, setIsOpen] = useState(false);
    const [step, setStep] = useState<number>(1);
    const [filter, setFilter] = useState(ADDONS_TV);
    const [riskMessage, setRiskMessage] = useState<string>();
    const [isOtpSuccess, setIsOtpSuccess] = useState<boolean>(false);
    const [riskColor, setRiskColor] = useState<Colors>();
    const [stratum, setStratum] = useState<number | undefined>();
    const [priceOfPlans, setPriceOfPlans] = useState<number[]>([]);
    const [addOnsServices, setAddOnsServices] = useState<IAddOns>({ collection: [] });
    const [addNewAddOns, setAddNewAddOns] = useState<boolean>(false);
    const [documentType, setDocumentType] = useState<string | undefined>();
    const [catalogCharges, setCatalogCharges] = useState<ICatalogCharges[]>([]);
    const [identificationDocument, setIdentificationDocument] = useState<string | undefined>();
    const [subscriptionsForService, setSubscriptionsForService] = useState<ISubscriptionsId[]>();
    const [processCompletedSuccessfully, setProcessCompletedSuccessfully] = useState(false);
    const [successScheduleInstallation, setSuccessScheduleInstallation] = useState(false);
    const [subscriptionsForAccountIds, setSubscriptionsForAccountsIds] = useState<ISubscriptionsCollection>();
    const [filteredData, setFilteredData] = useState<IRenderComponentStep[]>();
    const [stepperRenderComponent, setStepperRenderComponent] = useState<IRenderComponentStep[]>();
    const [successLoading, setSuccessLoading] = useState<boolean>(false);
    const [renderComponents, setRenderComponents] = useState<IRenderComponentStep[]>([]);
    const [riskApproved, setRiskApproved] = useState<boolean>(false);
    const [scheduleInstallation, setScheduleInstallation] = useState<ISlot>();
    const [scheduleSuccess, setScheduleSuccess] = useState<boolean>(false);
    const [responseSubscriptionsId, setSubscriptionsId] = useState<IServiceSubscription[]>([]);
    const [successRisk, setSuccessRisk] = useState<ITigoRiskManagementResponse | null>();
    const [customerInfo, setCustomerInfo] = useState<ITigoSalesProspectLead>();
    const [actualBillCycle, setActualBillCycle] = useState<{ billCycleOpenDate: Date; billCycleEndDate: Date }>({
        billCycleOpenDate: new Date(),
        billCycleEndDate: new Date(),
    });

    const validationStatus = useRef(0);
    const { t } = useTranslation(["customer"]);

    const refreshAddons = useAddonStore((state) => state.refreshAddons);
    const setRefreshAddons = useAddonStore((state) => state.setRefreshAddons);

    const [catalogChargesTemp, setCatalogChargesTemp] = useState<Map<string, ICatalogCharges>>(new Map());

    useEffect(() => {
        setRefreshAddons(true);
    }, [contactDetails]);

    useEffect(() => {
        if (refreshAddons) {
            setAddOnsServices({ collection: [] });
        }
    }, [refreshAddons]);

    useEffect(() => {
        if (contactDetails) {
            setIdentificationDocument(contactDetails?.identityDocuments[0]?.identifier);
            setStratum(Number(contactDetails?.contactAddresses[0]?.streetQualifier) ?? undefined);
            setDocumentType(contactDetails?.identityDocuments[0]?.type ?? "");
        }
    }, [contactDetails]);

    const names = [
        { name: ADDONS_TV, label: "ADDON TV" },
        { name: ADDONS_INTERNET, label: "ADDON INTERNET" },
        { name: ADDONS_MATERIAL, label: "ADDON MATERIAL" },
    ];

    const onClickOpenOffer = () => {
        setAddNewAddOns(true);
    };

    const onClickCloseOffer = () => {
        setAddNewAddOns(false);
    };

    const getCorrelationId = async () => {
        const { reference } = await getUniqueService();
        setCorrelationId(reference);
    };

    function parseInvoiceDate(dateString: string): Date {
        const year = dateString.substring(0, 4);
        const month = dateString.substring(4, 6);
        const day = dateString.substring(6, 8);
        const hours = dateString.substring(8, 10);
        const minutes = dateString.substring(10, 12);
        const seconds = dateString.substring(12, 14);

        return new Date(`${year}-${month}-${day}T${hours}:${minutes}:${seconds}`);
    }

    useEffect(() => {
        if (accountsCbs) {
            setActualBillCycle({
                billCycleOpenDate: new Date(parseInvoiceDate(accountsCbs.account[0].acctInfo.billCycleOpenDate)),
                billCycleEndDate: new Date(parseInvoiceDate(accountsCbs.account[0].acctInfo.billCycleEndDate)),
            });
        }
    }, [accountsCbs]);

    const validateCancelAddOn = (
        activateDate: string,
        _addOnStatus: string,
        billCycleEndDate: Date,
        billCycleOpenDate: Date
    ): boolean => {
        const validStatus = _addOnStatus === STATUS_ADD_ONS.ACTIVE || _addOnStatus === "NO_ONGOING_REQUEST";
        if (!validStatus) {
            return false;
        }

        const activation = dayjs(activateDate);
        const cycleOpen = dayjs(billCycleOpenDate);
        const cycleEndMinusOneDay = dayjs(billCycleEndDate).subtract(1, "day");

        const isInCurrentCycle = activation.isSameOrAfter(cycleOpen) && activation.isSameOrBefore(cycleEndMinusOneDay);

        return !isInCurrentCycle;
    };

    const localStorageKey = localStorage.getItem("USER_SELECTED_CHANNEL");
    const channelLocalStorage = localStorageKey ? JSON.parse(localStorageKey) : null;
    const channelCode = channelLocalStorage?.code ?? channel?.code;

    const removeAddOn = (addonsId: number, serviceId: number, cycleDate: string) => {
        return fetcher(removeAddonActionApiRoute(addonsId, cycleDate, channelCode, serviceId), {
            method: "DELETE",
        });
    };

    const confirmSchedule = async () => {
        try {
            getCorrelationId();
            const formattedDate = scheduleInstallation?.start
                ? format(new Date(scheduleInstallation.start), "yyyy-MM-dd")
                : "";

            const billingAddress = contactDetails?.contactAddresses.find(
                (contacAddress) => contacAddress.type === "BILLING"
            );

            const getHours = scheduleInstallation?.start ? new Date(scheduleInstallation.start).getHours() : 0;
            const timeSlotCode = getHours !== null && getHours < 12 ? TIME_SLOT_CODE_AM : TIME_SLOT_CODE_PM;
            const addressId = billingAddress ? billingAddress.id : undefined;

            const response = await createAppointment({
                addressId: addressId ? Number(addressId) : 0,
                contactUuid: contactDetails?.contactUuid ?? "",
                reasonCode: REASON_CODE_INSTALL,
                source: SOURCE_CODE_UI,
                timeSlotCode: timeSlotCode,
                duration: 30,
                at: formattedDate,
                correlationId: correlationId ?? "",
            });

            if (response) {
                setScheduleSuccess(true);
            }
        } catch (err) {
            setError(err);
        }
    };

    const onClickConfirmSchedule = () => {
        confirmSchedule();
    };

    const handleNext = () => {
        if (step <= renderComponents.length - 1) {
            setStep((prevStep) => prevStep + 1);
        }
    };

    useEffect(() => {
        const filtered = renderComponents.filter((_, index) => step === index + 1);

        if (filtered.length > 0) {
            if (typeof filtered[0].function === "function") {
                filtered[0].function();
            }

            setFilteredData(filtered);
            setStepperRenderComponent((prev) => ({
                ...prev,
                ...filtered,
            }));
        }
    }, [step, renderComponents]);

    const getSubcriptionsForAccount = async (query: string) => {
        try {
            startFetching();
            const response = await getSubscriptionsAccountIds(query);
            if (response?.collection && response.collection.length > 0) {
                const updatedCollection = await Promise.all(
                    response.collection.map(async (item) => {
                        let terminationStatusValue = false;
                        try {
                            const terminationStatus = await getSubscriptionsTerminationStatus(item.id.toString());
                            if (terminationStatus && terminationStatus.length > 0 && terminationStatus[0].status) {
                                terminationStatusValue = true;
                            }
                        } catch (terminationError) {
                            console.error("Error fetching termination status", terminationError);
                        }

                        return { ...item, terminationStatus: terminationStatusValue };
                    })
                );
                setSubscriptionsForAccountsIds({ ...response, collection: updatedCollection });
            } else {
                setSubscriptionsForAccountsIds(response);
            }
            endFetching();
        } catch (err) {
            setError(err);
        }
    };

    const getSubscriptionForService = async (query: string) => {
        try {
            startFetching();
            const response = await getSubcriptionsId(query);
            setSubscriptionsForService((prevSubscriptions = []) => [...prevSubscriptions, response]);
            endFetching();
        } catch (err) {
            setError(err);
        }
    };

    const getCatlogChargeForService = async (query: string) => {
        try {
            const response = await getCatalogCharges(query);
            setCatalogCharges((prevCharges = []) => [...prevCharges, response]);
        } catch (err) {
            setError(err);
        }
    };

    const getAddOnsForServices = async (query: string) => {
        const response = await getAddOnsForService(query);

        setAddOnsServices((prevAddOnsServices) => {
            if (!prevAddOnsServices || prevAddOnsServices.collection.length === 0) {
                return response;
            }

            const combinedCollection = [...prevAddOnsServices.collection, ...response.collection];

            const uniqueByKey = new Map<string, (typeof response.collection)[number]>();

            for (const item of combinedCollection) {
                const key = item.id != null ? `id:${item.id}` : `code:${item.code}|svc:${item.serviceId}`;
                if (!uniqueByKey.has(key)) uniqueByKey.set(key, item);
            }

            const uniqueCollection = Array.from(uniqueByKey.values());

            return {
                ...response,
                collection: uniqueCollection,
            };
        });
    };

    const getServicesSubscriptionsId = async (query: string) => {
        try {
            const response = await getServicesSubscriptions(query);
            setSubscriptionsId((prevSubscriptions = []) => [...prevSubscriptions, response]);
        } catch (err) {
            setError(err);
        }
    };

    /*useEffect(() => {
        console.log("subscriptionsForAccountIds", subscriptionsForAccountIds);
        if (subscriptionsForAccountIds && !successLoading && refreshAddons) {
            (async () => {
                for (const element of subscriptionsForAccountIds.collection) {
                    const id = element.id.toString();
                    await getSubscriptionForService(id);
                    await getServicesSubscriptionsId(id);
                }
                setSuccessLoading(true);
            })();
        }
    }, [subscriptionsForAccountIds, successLoading, refreshAddons]);*/

    useEffect(() => {
        if (subscriptionsForAccountIds && !successLoading && refreshAddons) {
            subscriptionsForAccountIds.collection.forEach((element) => {
                getSubscriptionForService(element.id.toString());
                getServicesSubscriptionsId(element.id.toString());
            });
            setSuccessLoading(true);
        }
    }, [subscriptionsForAccountIds, successLoading, refreshAddons]);

    useEffect(() => {
        if (responseSubscriptionsId?.length && refreshAddons) {
            (async () => {
                // junta todos los serviceId (únicos) de todas las colecciones
                const ids = new Set<number>();
                for (const { collection } of responseSubscriptionsId) {
                    for (const svc of collection) {
                        if (svc?.id != null) ids.add(svc.id);
                    }
                }

                // dispara todas las llamadas y espera a que terminen
                const results = await Promise.allSettled(
                    Array.from(ids).map((id) => 
                        getAddOnsForServices(id.toString())
                ));

                // loguea los errores sin detener la ejecución
                results.forEach((result, index) => {
                    if (result.status === 'rejected') {
                        const id = Array.from(ids)[index];
                        console.error(`Error retrieving add-ons - ID ${id}:`, result.reason);
                    }
                });

                setRefreshAddons(false);
            })();
        }
    }, [responseSubscriptionsId, refreshAddons]);

    /*useEffect(() => {
        if (subscriptionsForService && refreshAddons) {
            subscriptionsForService.forEach((element) => {
                element.charges?.[0]?.catalogCode && getCatlogChargeForService(element.charges[0].catalogCode);
            });
        }
        console.log("subscriptionsForService");
        console.log("subscriptionsForService", subscriptionsForService);
        console.log("refreshAddons", refreshAddons);
    }, [subscriptionsForService, refreshAddons]); */

    useEffect(() => {
        //if (subscriptionsForService && refreshAddons) {
        if (subscriptionsForService) {
            (async () => {
                for (const element of subscriptionsForService) {
                    const code = element.charges?.[0]?.catalogCode;
                    if (code) {
                        await getCatlogChargeForService(code);
                    }
                }
            })();
        }
    }, [subscriptionsForService, refreshAddons]);

    // update catalogChargesTemp and priceOfPlans when catalogCharges change temp

    useEffect(() => {
        if (catalogCharges.length > 0) {
            const { cartVatIncluded } = getConfig();

            setCatalogChargesTemp((prevMap) => {
                const updatedMap = new Map(prevMap);

                catalogCharges.forEach((charge) => {
                    const existing = updatedMap.get(charge.code);
                    const currentPrice = cartVatIncluded
                        ? charge.pricePlans[0].defaultPrice
                        : charge.pricePlans[0].defaultPriceVatExcluded;

                    const existingPrice = existing
                        ? cartVatIncluded
                            ? existing.pricePlans[0].defaultPrice
                            : existing.pricePlans[0].defaultPriceVatExcluded
                        : null;

                    // Agregar o actualizar si el precio cambió
                    if (!existing || currentPrice !== existingPrice) {
                        updatedMap.set(charge.code, charge);
                    }
                });

                // Actualizar los precios desde el mapa actualizado
                const newPrices = Array.from(updatedMap.values()).map((charge) =>
                    cartVatIncluded ? charge.pricePlans[0].defaultPrice : charge.pricePlans[0].defaultPriceVatExcluded
                );

                setPriceOfPlans(newPrices);

                return updatedMap;
            });
        }
    }, [catalogCharges]);

    /*useEffect(() => {
        if (catalogCharges.length > 0) {
            const { cartVatIncluded } = getConfig();
            const newPrices = catalogCharges.map((charge) =>
                cartVatIncluded ? charge.pricePlans[0].defaultPrice : charge.pricePlans[0].defaultPriceVatExcluded
            );

            setPriceOfPlans(newPrices);
        } 
        console.log("catalogCharges", catalogCharges);
    }, [catalogCharges]); */

    const debouncedHandleChange = useCallback((event: SelectChangeEvent<typeof filter>) => {
        debounce(() => {
            setFilter(event.target.value);
        }, 300)();
    }, []);

    const [addressAccount, setAddressAccount] = useState<IAddress | undefined>();

    useEffect(() => {
        const filterAddress = contactDetails?.contactAddresses?.find(
            (_address) => _address.type === EAddressType.INSTALLATION // cambio a INSTALLATION
        );

        setAddressAccount(filterAddress);
    }, [contactDetails]);

    function shortUUID(length = 5): string {
        return uuidv4().replace(/-/g, "").substring(0, length).toUpperCase();
    }

    const verifyRisk = async (props: {
        addressAccount: IAddress | undefined;
        channel: IKeycloackChannel | undefined;
        identificationDocument: string | undefined;
        stratum: number | undefined;
        personalInfo: IContact | undefined;
        documentType: string | undefined;
    }) => {
        if (
            props.addressAccount &&
            props.channel &&
            props.identificationDocument &&
            props.stratum !== undefined &&
            props.documentType
        ) {
            try {
                startFetching();
                const riskResponse = await postTigoSalesRiskManagement({
                    transactionType: ERiskTransactionType.ACTIVATION,
                    traceID: shortUUID(),
                    customer: {
                        id: props.identificationDocument,
                        typeIdentification: props.documentType,
                        stratum: Number(props.stratum),
                        firstName: props.personalInfo?.firstName,
                        firstLastName: props.personalInfo?.lastName,
                    },
                    departmentCode: "05",
                    municipalityCode: "05631",
                    channel: props.channel.code,
                });

                endFetching();

                if (riskResponse) {
                    switch (riskResponse.code) {
                        case ERiskCodeType.APPROVED:
                            setRiskApproved(true);
                            setRiskMessage(t("customer:riskValidation100Message"));
                            setRiskColor(Colors.green);
                            validationStatus.current++;
                            break;
                        case ERiskCodeType.UPFRONT_PAYMENT:
                            setRiskMessage(t("customer:riskValidation101Message"));
                            setRiskColor(Colors.orange);
                            validationStatus.current++;
                            break;
                        case ERiskCodeType.REJECTED:
                            setRiskMessage(t("customer:riskValidationRejectMessage"));
                            setAddNewAddOns(false);
                            setRiskColor(Colors.red);
                            break;
                        default:
                            setRiskMessage(t("customer:riskValidationErrorMessage"));
                            setAddNewAddOns(false);
                            setRiskColor(Colors.red);
                            break;
                    }

                    setSuccessRisk(riskResponse);

                    return riskResponse;
                }
                setRiskMessage(t("customer:riskValidationErrorMessage"));
                setRiskColor(Colors.red);

                return null;
            } catch (e) {
                endFetching();
                setRiskMessage(t("customer:riskValidationErrorMessage"));
                setRiskColor(Colors.red);

                return null;
            }
        } else {
            return null;
        }
    };

    const personalInfo = contactDetails?.person;

    useEffect(() => {
        if (isOpen && (otpSmsSuccess || otpEmailSuccess)) {
            verifyRisk({ addressAccount, channel, identificationDocument, stratum, documentType, personalInfo });
        }
    }, [
        isOpen,
        otpSmsSuccess,
        otpEmailSuccess,
        contactDetails?.person,
        addressAccount,
        channel,
        identificationDocument,
        stratum,
        documentType,
        personalInfo,
    ]);

    useEffect(() => {
        if (riskMessage) {
            const componentToRender = () => (
                <div>
                    <div className={classes.flexContainer}>
                        <div className={classes.columnLayout}>
                            <span
                                style={{
                                    backgroundColor: `${riskColor}`,
                                    width: "4px",
                                    height: "100%",
                                    display: "block",
                                }}
                            />
                        </div>
                        <div className={classes.columnLayout}>
                            <div className={`${classes.rowLayout} ${classes.sectionSubtitle}`}>
                                {t("customer:riskValidationSubtitle")}
                            </div>
                            <div className={classes.rowLayout}>{riskMessage}</div>
                        </div>
                    </div>
                </div>
            );

            setRenderComponents([
                {
                    component: componentToRender,
                    title: t("customer:riskValidation"),
                    function: () =>
                        verifyRisk({
                            addressAccount,
                            channel,
                            identificationDocument,
                            stratum,
                            documentType,
                            personalInfo,
                        }),
                },
            ]);
        }
    }, [
        riskMessage,
        riskColor,
        t,
        addressAccount,
        channel,
        identificationDocument,
        stratum,
        documentType,
        personalInfo,
    ]);

    return {
        error,
        filter,
        names,
        isOpen,
        isLoading,
        setIsOpen,
        step,
        setStep,
        priceOfPlans,
        addNewAddOns,
        debouncedHandleChange,
        handleNext,
        filteredData,
        stepperRenderComponent,
        setStepperRenderComponent,
        addOnsServices,
        onClickOpenOffer,
        renderComponents,
        onClickCloseOffer,
        subscriptionsForAccountIds,
        getSubcriptionsForAccount,
        setSuccessScheduleInstallation,
        processCompletedSuccessfully,
        isOtpSuccess,
        setIsOtpSuccess,
        removeAddOn,
        successScheduleInstallation,
        setProcessCompletedSuccessfully,
        riskApproved,
        setScheduleInstallation,
        onClickConfirmSchedule,
        setCorrelationId,
        successRisk,
        setSuccessRisk,
        scheduleSuccess,
        correlationId,
        setScheduleSuccess,
        validateCancelAddOn,
        actualBillCycle,
        customerInfo,
        setCustomerInfo,
        setAddNewAddOns,
        scheduleInstallation,
        catalogCharges,
        setRefreshAddons,
        catalogChargesTemp,
        setCatalogChargesTemp,
    };
};
