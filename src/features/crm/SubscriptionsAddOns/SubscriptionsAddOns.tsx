import { ReactComponent as HistoryIcon } from "@static/icons/history.svg";
import { useEffect, useState } from "react";
import { useMemo } from "react";
// import { ChangeOfferProcess } from "./Components/ChangeOfferProcess/ChangeOfferProcess";
import {
    HandleErrorAndLoading,
    SearchSkeleton,
    SectionCard,
    SimpleMultiStepDialog,
    useOutlinedCardStyle,
    useSectionIconStyle,
} from "@common";
import { Button, Card, CardContent, Divider, Grid, IconButton, SvgIcon, Tooltip, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import Subscriptions from "./Components/Subscriptions";
import { useStyle } from "./style";
import { useSubscriptionsAddOns } from "./useSubscriptionsAddOns";
// import AddonsWithOtp from "./Components/AddonsWithOtp/AddonsWithOtp";
import { IBillCycle } from "../AccountDetails/AccountDetailsHeader/Billing/BillCycle/IBillCycle";
import { TContactDetails } from "../CustomerDetails/ICustomerDetails";
import AddNewOffer from "./Components/AddNewOffer/AddNewOffer";
import AddNewRetention from "./Components/AddNewRetention/AddNewRetention";
import AddonsCancellation from "./Components/AddOns/AddonsCancellation";
// import SubscriptionWithOtpDialog from "./Components/AddOfferProcess/dialogs/SubscriptionWithOtpDialog";
import { ITigoCbsAccountsResponse } from "@modules/tigoSalesFacade/interfaces/responses/ITigoCbsAccountsResponse";
// import { IotpEnum } from "@features/acquisition/PostpayAcquisition/Postpay/common/AddressInstallationStep/NormalizedAddressTigoForm/INormalizedAddressTigoForm";
import PersonRemoveIcon from "@mui/icons-material/PersonRemove";
import SuspentionInformation from "./Views/SuspentionInformation/SuspentionInformation";
import ScheduleInstallation from "./Views/ScheduleInstallation/ScheduleInstallation";
// import { IFetchedData, keycloakUtils } from "itsf-ui-common";
import { IFetchedData } from "itsf-ui-common";
import { useBillCycle } from "../AccountDetails/AccountDetailsHeader/Billing/BillCycle/useBillCycle";
import { IAccountDetails } from "../CustomerPanel/ICustomer";

import { useCycleDate } from "@common/CycleDate/useCycleDate";
// import { ICatalogCharges } from "@services/subscription/accountId/interface/ICatalogs";
// import { ISubscriptionsAccountIds } from "@services/subscription/accountId/interface/ISubscriptionsAccountIds";
// import EligibilityVerificationDialog from "./Components/AddOfferProcess/dialogs/EligibilityVerificationDialog";
// import { ERiskCodeType } from "@modules/tigoSalesFacade/interfaces/responses/ITigoRiskManagementResponse";
// import { AddOfferProcess } from "./Components/AddOfferProcess/AddOfferProcess";
import MenuAddOns from "@features/crm/SubscriptionsAddOns/MenuAddOns";
// import { ADDONS_PERMISSION, RULES_OF_USER } from "@constants";
import ReplayIcon from "@mui/icons-material/Replay";
import { useAddonStore } from "./useAddonStore";
import CancelService from "./Components/CancelService/CancelService";
import AddonsDeliveredStoreCart from "./Components/AddOns/AddonsDeliveredStoreCart";

interface IProps {
    accountId: string;
    contactDetails: TContactDetails | undefined;
    billCycle: IBillCycle | undefined;
    accountsCbs: ITigoCbsAccountsResponse | undefined;
    account: IFetchedData<IAccountDetails | undefined>;
    catalogCharges?: any;
}

export const SubscriptionsAddOns = ({ accountId, contactDetails, accountsCbs, account }: IProps) => {
    const { t } = useTranslation(["common", "customer", "acquisition"]);
    const { classes: outlinedCardClasses } = useOutlinedCardStyle();
    const { classes } = useStyle();
    const { classes: iconClasses } = useSectionIconStyle();
    const [isActiveRetention, setIsActiveRetention] = useState(false);
    const { calculateCycleDate } = useCycleDate();
    // const [selectedAddonId, setSelectedAddonId] = useState(0);
    // const [selectedAddonId] = useState(0);
    // const [selectedAddonCode, setSelectedAddonCode] = useState("");
    const [selectedAddonCode] = useState("");
    // const [isOtpDialogOpen, setIsOtpDialogOpen] = useState(false);
    // const [selectedServiceGroup, setSelectedServiceGroup] = useState<string>();
    const [selectedServiceGroup] = useState<string>();
    // const [isAddonsWithOtpOpen, setIsAddonsWithOtpOpen] = useState(false);
    // const [otpMethod, setOtpMethod] = useState<string>(IotpEnum.Sms);
    const [suspendeInformation, setSuspendeInformation] = useState<boolean>(false);
    // const [showChangeProcess, setShowChangeProcess] = useState<boolean>(false);
    const [showChangeProcess] = useState<boolean>(false);
    // const [showAddProcess, setShowAddProcess] = useState<boolean>(false);
    const [showAddProcess] = useState<boolean>(false);
    const [showSchedule, setShowSchedule] = useState<boolean>(false);
    const [orderIdForSchedule, setOrderIdForSchedule] = useState<string>("");
    const [openValidationOtp, setOpenValidationOtp] = useState<boolean>(false);
    // const [openCancelElegibility, setOpenCancelElegibility] = useState(false);
    const [openCancelElegibility] = useState(false);
    const [otpEmailSuccess, setOtpEmailSuccess] = useState<boolean>(false);
    const [otpSmsSuccess, setOtpSmsSuccess] = useState<boolean>(false);
    // const [currentOffer, setCurrentOffer] = useState<ICatalogCharges[]>([]);
    // const [currentSubscription, setCurrentSubscription] = useState<ISubscriptionsAccountIds>();
    // const [currentSubscription] = useState<ISubscriptionsAccountIds>();
    // const [isAddNewSubscriptionDialogOpen, setIsAddNewSubscriptionDialogOpen] = useState(false);
    const [showCancellationAddon, setShowCancellationAddon] = useState<boolean>(false);
    const [showCancellationService, setShowCancellationService] = useState<boolean>(false);
    const [selectedServiceIds, setSelectedServiceIds] = useState<string[]>([]);
    const [componentScheduleTitle, setComponentScheduleTitle] = useState<string>("");

    // State for delivered store cart
    const [showDeliveredStoreCart, setShowDeliveredStoreCart] = useState(false);

    const setRefreshAddons = useAddonStore((state) => state.setRefreshAddons);

    const {
        addNewAddOns,
        addOnsServices,
        error,
        filter,
        removeAddOn,
        filteredData,
        getSubcriptionsForAccount,
        debouncedHandleChange,
        handleNext,
        isLoading,
        isOpen,
        names,
        onClickCloseOffer,
        onClickOpenOffer,
        priceOfPlans,
        renderComponents,
        setStep,
        step,
        subscriptionsForAccountIds,
        setIsOpen,
        riskApproved,
        setProcessCompletedSuccessfully,
        setSuccessScheduleInstallation,
        successScheduleInstallation,
        scheduleInstallation,
        // setIsOtpSuccess,
        setScheduleInstallation,
        setCorrelationId,
        onClickConfirmSchedule,
        scheduleSuccess,
        setScheduleSuccess,
        setAddNewAddOns,
        correlationId,
        validateCancelAddOn,
        actualBillCycle,
        // catalogCharges,
        // successRisk,
        setSuccessRisk,
        setStepperRenderComponent,
        catalogChargesTemp,
        setCatalogChargesTemp,
    } = useSubscriptionsAddOns({
        contactDetails,
        accountsCbs,
        otpEmailSuccess,
        otpSmsSuccess,
    });

    const { getBillCycle, billCycleState, billCycleId } = useBillCycle(accountId);

    useEffect(() => {
        if (accountId) {
            getSubcriptionsForAccount(accountId);
        }
    }, [accountId]);

    // useEffect(() => {
    //     if (otpEmailSuccess !== true && otpSmsSuccess !== true && !isAddonsWithOtpOpen) {
    //         setAddNewAddOns(false);
    //     } else if ((otpEmailSuccess || otpSmsSuccess) && !isAddonsWithOtpOpen) {
    //         setAddNewAddOns(true);
    //     }
    // }, [isAddonsWithOtpOpen, otpEmailSuccess, otpSmsSuccess, setAddNewAddOns]);

    // const handleActiveRetention = (addonCode: string, addonId: number) => {
    const handleActiveRetention = () => {
        // setSelectedAddonId(addonId);
        // setSelectedAddonCode(addonCode);
        setIsActiveRetention(true);
    };

    const handleInactiveRetention = () => {
        setIsActiveRetention(false);
    };

    const handleDirectOpenAddNewOffer = () => {
        setAddNewAddOns(true);

        setOtpSmsSuccess(true);
        setOtpEmailSuccess(true);

        onClickOpenOffer();
        setShowCancellationAddon(false);
    };

    const handleShowSchedule = (show: boolean) => {
        setShowSchedule(show);
    };

    const handleDirectOpenCancelationAddons = () => {
        setShowCancellationAddon(true);
    };

    // const handleOpenAddonsWithOtp = () => {
    //     onClickOpenOffer();
    //     setIsAddonsWithOtpOpen(true);
    // };

    // const handleStartChangeProcess = (
    //     currentOfferResponse: ICatalogCharges[],
    //     subscription: ISubscriptionsAccountIds
    // ) => {
    //     // setCurrentOffer(currentOfferResponse);
    //     setCurrentSubscription(subscription);
    // };

    // const userCanBeAddNewAddons = keycloakUtils.hasResourceRole(
    //     ADDONS_PERMISSION.PURCHASE_ADDONS,
    //     RULES_OF_USER.PERMISSION_TYPE.CUSTOMER
    // );

    const handleAddServiceId = (serviceId: string) => {
        setSelectedServiceIds((prev) => (prev.includes(serviceId) ? prev : [...prev, serviceId]));
    };

    // Compute all subscription ids
    const allSubscriptionIds = subscriptionsForAccountIds?.collection.map((el) => String(el.id)) || [];

    // Filter MATERIAL add-ons for AddonsDeliveredStoreCart
    const [materialAddonsFromCancelService, setMaterialAddonsFromCancelService] = useState<any[]>([]);
    const materialAddons = useMemo(() => {
        if (selectedServiceIds.length === 0) {
            return addOnsServices?.collection?.filter((a) => a.itemGroupCode === "MATERIAL") || [];
        }

        return materialAddonsFromCancelService;
    }, [selectedServiceIds, addOnsServices, materialAddonsFromCancelService]);

    // Handlers for AddonsDeliveredStoreCart
    const handleConfirmDeliveredEquipment = async (updatedAddons) => {
        setShowDeliveredStoreCart(false);
        setShowCancellationService(false);
        await Promise.resolve();

        return true;
    };
    const handleCancelDeliveredStoreCart = () => {
        setShowDeliveredStoreCart(false);
        setShowCancellationService(false);
    };
    const handleCloseFinalConfirmModal = () => {
        setShowDeliveredStoreCart(false);
        setShowCancellationService(false);
    };

    // @ts-ignore
    return (
        <Card className={outlinedCardClasses.main} elevation={0} variant="outlined">
            <CardContent>
                <HandleErrorAndLoading error={error} isLoading={isLoading} skeleton={<SearchSkeleton height={200} />}>
                    <SectionCard
                        actionButtons={
                            <div>
                                {!suspendeInformation &&
                                    !showChangeProcess &&
                                    !showAddProcess &&
                                    !addNewAddOns &&
                                    !showSchedule &&
                                    !showCancellationAddon &&
                                    !showCancellationService &&
                                    !showDeliveredStoreCart && (
                                        <Tooltip title={t("customer:suspendSubscriptions")}>
                                            <IconButton
                                                aria-label="delete"
                                                color="primary"
                                                size="large"
                                                onClick={() => setOpenValidationOtp(true)}
                                            >
                                                <PersonRemoveIcon fontSize="inherit" />
                                            </IconButton>
                                        </Tooltip>
                                    )}
                            </div>
                        }
                        icon={<SvgIcon className={iconClasses.main} component={HistoryIcon} viewBox="0 0 21 19" />}
                        title={t("customer:subscriptionsAddOns")}
                    >
                        {openValidationOtp || suspendeInformation ? (
                            <SuspentionInformation
                                account={account}
                                accountId={accountId}
                                accountsCbs={accountsCbs}
                                contactDetails={contactDetails}
                                handleClose={() => setOpenValidationOtp(false)}
                                open={openValidationOtp}
                                setSuspendeInformation={setSuspendeInformation}
                                subscriptionsForAccountIds={subscriptionsForAccountIds}
                            />
                        ) : showSchedule ? (
                            <div>
                                <ScheduleInstallation
                                    callIdFromOrder={orderIdForSchedule}
                                    contact={contactDetails}
                                    scheduleSuccess={scheduleSuccess}
                                    setCorrelationId={setCorrelationId}
                                    setScheduleInstallation={setScheduleInstallation}
                                    setSuccessScheduleInstallation={setSuccessScheduleInstallation}
                                    componentScheduleTitle={componentScheduleTitle}
                                />
                            </div>
                        ) : showAddProcess && selectedServiceGroup ? (
                            <div>
                                {/*<AddOfferProcess*/}
                                {/*    accountId={accountId}*/}
                                {/*    closeProcess={() => setShowAddProcess(false)}*/}
                                {/*    contactDetails={contactDetails}*/}
                                {/*    serviceGroup={selectedServiceGroup}*/}
                                {/*/>*/}
                            </div>
                        ) : showChangeProcess ? (
                            <div>
                                {/*{currentSubscription ? (*/}
                                {/*    <ChangeOfferProcess*/}
                                {/*        accountId={accountId}*/}
                                {/*        contactDetails={contactDetails}*/}
                                {/*        currentOffer={currentOffer}*/}
                                {/*        currentSubscription={currentSubscription}*/}
                                {/*        setShowChangeProcess={setShowChangeProcess}*/}
                                {/*    />*/}
                                {/*) : null}*/}
                                null
                            </div>
                        ) : (
                            <div>
                                {!addNewAddOns &&
                                    !showCancellationAddon &&
                                    !showCancellationService &&
                                    !showDeliveredStoreCart && (
                                        <>
                                            <Grid
                                                alignItems="center"
                                                container
                                                direction="row"
                                                justifyContent="space-between"
                                                pb={1}
                                            >
                                                <Grid item>
                                                    <Typography
                                                        className={`${classes.titleAddOn} ${classes.subscriptionTitleContainer}`}
                                                    >
                                                        {t("customer:subscriptionsTitle")}
                                                    </Typography>
                                                </Grid>
                                                <Grid item>
                                                    <Button
                                                        className={classes.addSubscriptionButton}
                                                        color="primary"
                                                        size="small"
                                                        variant="outlined"
                                                        // onClick={() => setIsAddNewSubscriptionDialogOpen(true)}
                                                    >
                                                        {t("customer:addSubscription")}
                                                    </Button>
                                                </Grid>
                                            </Grid>
                                            {subscriptionsForAccountIds?.collection.map((element, index) => {
                                                return (
                                                    <Subscriptions
                                                        accountId={accountId}
                                                        catalogCharges={catalogChargesTemp}
                                                        classes={classes}
                                                        index={index}
                                                        key={`${element.id}-${element.name}`}
                                                        priceOfPlans={priceOfPlans}
                                                        serviceGroup={element.serviceGroup}
                                                        setShowCancellationService={setShowCancellationService}
                                                        subscriptions={element}
                                                        onAddServiceId={handleAddServiceId}
                                                        allSubscriptionIds={allSubscriptionIds}
                                                        subscriptionsForAccountIds={subscriptionsForAccountIds}
                                                    />
                                                );
                                            })}
                                        </>
                                    )}
                                {!openCancelElegibility &&
                                    !addNewAddOns &&
                                    !showSchedule &&
                                    !showCancellationAddon &&
                                    !showCancellationService &&
                                    !showDeliveredStoreCart && (
                                        <div>
                                            <Divider
                                                className={classes.dividerStyle}
                                                sx={{ display: addNewAddOns ? "none" : "block" }}
                                            />
                                            <Grid
                                                alignItems="center"
                                                container
                                                direction="row"
                                                justifyContent="space-between"
                                                pb={1}
                                                pt={1}
                                            >
                                                <Grid item>
                                                    <Typography
                                                        className={`${classes.titleAddOn} ${classes.subscriptionTitleContainer}`}
                                                    >
                                                        {t("customer:addOnsTitle")}
                                                    </Typography>
                                                </Grid>
                                                <Grid item>
                                                    <Grid alignItems="center" container spacing={1}>
                                                        <Grid item>
                                                            <Button
                                                                className={classes.refreshAddonButton}
                                                                color="primary"
                                                                size="small"
                                                                variant="outlined"
                                                                onClick={() => setRefreshAddons(true)}
                                                            >
                                                                <ReplayIcon />
                                                            </Button>
                                                        </Grid>
                                                        <Grid item>
                                                            <Button
                                                                className={classes.addSubscriptionButton}
                                                                size="small"
                                                                variant="outlined"
                                                                onClick={handleDirectOpenAddNewOffer}
                                                            >
                                                                {t("customer:addSubscription")}
                                                            </Button>
                                                        </Grid>
                                                        <Grid item>
                                                            <Button
                                                                className={classes.addSubscriptionButton}
                                                                size="small"
                                                                variant="outlined"
                                                                onClick={handleDirectOpenCancelationAddons}
                                                            >
                                                                {t("customer:cancel")}
                                                            </Button>
                                                        </Grid>
                                                        {/*{userCanBeAddNewAddons && (*/}
                                                        {/*    <Grid item>*/}
                                                        {/*        <Button*/}
                                                        {/*            className={classes.addSubscriptionButton}*/}
                                                        {/*            size="small"*/}
                                                        {/*            variant="outlined"*/}
                                                        {/*            // onClick={handleOpenAddonsWithOtp}*/}
                                                        {/*        >*/}
                                                        {/*            {t("customer:addSubscription")}*/}
                                                        {/*        </Button>*/}
                                                        {/*    </Grid>*/}
                                                        {/*)}*/}
                                                    </Grid>
                                                </Grid>
                                            </Grid>
                                        </div>
                                    )}
                                {!openCancelElegibility &&
                                !addNewAddOns &&
                                !showCancellationAddon &&
                                !showCancellationService &&
                                !showDeliveredStoreCart ? (
                                    <MenuAddOns
                                        accountsCbs={accountsCbs}
                                        actualBillCycle={actualBillCycle}
                                        addOnsServices={addOnsServices}
                                        billCycleId={billCycleId}
                                        billCycleState={billCycleState}
                                        calculateCycleDate={calculateCycleDate}
                                        classes={classes}
                                        contactDetails={contactDetails}
                                        debouncedHandleChange={debouncedHandleChange}
                                        filter={filter}
                                        getBillCycle={getBillCycle}
                                        handleActiveRetention={handleActiveRetention}
                                        handleShowSchedule={handleShowSchedule}
                                        names={names}
                                        removeAddOn={removeAddOn}
                                        setAddNewAddOns={setAddNewAddOns}
                                        setOrderIdForSchedule={setOrderIdForSchedule}
                                        setSuccessRisk={setSuccessRisk}
                                        subscriptions={subscriptionsForAccountIds}
                                        validateCancelAddOns={validateCancelAddOn}
                                        onClickOpenOffer={onClickOpenOffer}
                                    />
                                ) : (
                                    <div>
                                        <div>
                                            {!openCancelElegibility && isActiveRetention && (
                                                <>
                                                    <Typography
                                                        color={"primary"}
                                                        pb={0}
                                                        textAlign={"center"}
                                                        variant="h6"
                                                    >
                                                        {t("customer:retentionOffer")}
                                                    </Typography>
                                                    <Divider className={classes.dividerStyle} />
                                                    <AddNewRetention
                                                        // account={account}
                                                        addonCode={selectedAddonCode}
                                                        // addonId={selectedAddonId}
                                                        // data={addOnsServices}
                                                        onClickCloseOffer={() => {
                                                            onClickCloseOffer();
                                                            handleInactiveRetention();
                                                        }}
                                                    />
                                                </>
                                            )}
                                        </div>
                                    </div>
                                )}
                                {/*<AddonsWithOtp*/}
                                {/*    contactDetails={contactDetails}*/}
                                {/*    handleClose={() => {*/}
                                {/*        setIsAddonsWithOtpOpen(false);*/}
                                {/*    }}*/}
                                {/*    nextView={setIsOpen}*/}
                                {/*    open={isAddonsWithOtpOpen}*/}
                                {/*    setOpen={() => true}*/}
                                {/*    setOtpEmailSuccess={setOtpEmailSuccess}*/}
                                {/*    setOtpSmsSuccess={setOtpSmsSuccess}*/}
                                {/*/>*/}
                                {/*{isAddNewSubscriptionDialogOpen && (*/}
                                {/*    <EligibilityVerificationDialog*/}
                                {/*        accountId={accountId}*/}
                                {/*        currentServices={subscriptionsForAccountIds?.collection ?? []}*/}
                                {/*        handleClose={() => setIsAddNewSubscriptionDialogOpen(false)}*/}
                                {/*        handleNext={(serviceGroup) => {*/}
                                {/*            if (serviceGroup) {*/}
                                {/*                setIsOtpDialogOpen(true);*/}
                                {/*                setSelectedServiceGroup(serviceGroup);*/}
                                {/*            }*/}
                                {/*        }}*/}
                                {/*        open={isAddNewSubscriptionDialogOpen}*/}
                                {/*    />*/}
                                {/*)}*/}
                                {/*<SubscriptionWithOtpDialog*/}
                                {/*    contactDetails={contactDetails}*/}
                                {/*    handleClose={() => {*/}
                                {/*        setIsOtpDialogOpen(false);*/}
                                {/*    }}*/}
                                {/*    handleNext={() => setShowAddProcess(true)}*/}
                                {/*    open={isOtpDialogOpen}*/}
                                {/*    otpMethod={otpMethod}*/}
                                {/*    setIsOtpSuccess={setIsOtpSuccess}*/}
                                {/*    setOtpMethod={setOtpMethod}*/}
                                {/*/>*/}
                            </div>
                        )}
                        <div>
                            {/*{successRisk?.code === ERiskCodeType.APPROVED &&*/}
                            {addNewAddOns && (otpSmsSuccess || otpEmailSuccess) && (
                                <div>
                                    <Typography color={"primary"} pb={3} textAlign={"center"} variant="h5">
                                        {/*{t("customer:addNewAddOns")}*/}
                                    </Typography>
                                    <AddNewOffer
                                        addons={addOnsServices}
                                        contactDetails={contactDetails}
                                        correlationId={correlationId}
                                        scheduleInstallation={scheduleInstallation}
                                        scheduleSuccess={scheduleSuccess}
                                        setAddNewAddOns={setAddNewAddOns}
                                        setCorrelationId={setCorrelationId}
                                        setScheduleInstallation={setScheduleInstallation}
                                        setScheduleSuccess={setScheduleSuccess}
                                        setStepperRenderComponent={setStepperRenderComponent}
                                        setSuccessRisk={setSuccessRisk}
                                        setSuccessScheduleInstallation={setSuccessScheduleInstallation}
                                        subscriptions={subscriptionsForAccountIds}
                                        successScheduleInstallation={successScheduleInstallation}
                                        onClickCloseOffer={onClickCloseOffer}
                                        onClickConfirmSchedule={onClickConfirmSchedule}
                                        setComponentScheduleTitle = {setComponentScheduleTitle}
                                    />
                                </div>
                            )}
                            {showCancellationAddon && (
                                <div>
                                    <AddonsCancellation
                                        OrderIdForSchedule={orderIdForSchedule}
                                        accountId={accountId}
                                        addOnsServices={addOnsServices}
                                        contactDetails={contactDetails}
                                        handleShowSchedule={handleShowSchedule}
                                        names={names}
                                        setOrderIdForSchedule={setOrderIdForSchedule}
                                        setShowCancellationAddon={setShowCancellationAddon}
                                        setComponentScheduleTitle={setComponentScheduleTitle}
                                    />
                                </div>
                            )}
                            {showCancellationService && (
                                <div>
                                    <CancelService
                                        OrderIdForSchedule={orderIdForSchedule}
                                        accountId={accountId}
                                        addOnsServices={addOnsServices}
                                        contactDetails={contactDetails}
                                        handleShowSchedule={setShowSchedule}
                                        names={names}
                                        selectedServiceIds={selectedServiceIds}
                                        setOrderIdForSchedule={setOrderIdForSchedule}
                                        setSelectedServiceIds={setSelectedServiceIds}
                                        setShowCancellationService={setShowCancellationService}
                                        setShowDeliveredStoreCart={setShowDeliveredStoreCart}
                                        showDeliveredStoreCart={showDeliveredStoreCart}
                                        onMaterialAddonsChange={setMaterialAddonsFromCancelService}
                                    />
                                </div>
                            )}
                        </div>
                    </SectionCard>
                </HandleErrorAndLoading>
            </CardContent>
            <SimpleMultiStepDialog
                buttonClose={false}
                closeName="Continuar"
                disableEscapeKeyDown
                disabled={false}
                fullWidth
                handleClose={(_event?: object, reason?: string) => {
                    if (reason === "backdropClick" || reason === "escapeKeyDown" || isLoading) {
                        return;
                    }

                    if (riskApproved) {
                        setProcessCompletedSuccessfully(true);
                    }

                    setStep(1);
                    setIsOpen(false);
                }}
                handleNext={handleNext}
                maxWidth="md"
                open={isOpen}
                setStep={setStep}
                sizeOfPage={renderComponents?.length ?? 0}
                step={step ?? 0}
                title={filteredData?.length ? filteredData?.[0]?.title : ""}
            >
                {filteredData?.length && typeof filteredData[0]?.component === "function"
                    ? filteredData[0].component()
                    : filteredData?.[0]?.component}
            </SimpleMultiStepDialog>
            <AddonsDeliveredStoreCart
                addedAddons={materialAddons}
                isVisible={showDeliveredStoreCart}
                onCancel={handleCancelDeliveredStoreCart}
                onClose={handleCloseFinalConfirmModal}
                onConfirm={handleConfirmDeliveredEquipment}
            />
            {/*<AddonsWithOtp*/}
            {/*    contactDetails={contactDetails}*/}
            {/*    handleClose={() => {*/}
            {/*        setIsAddonsWithOtpOpen(false);*/}
            {/*    }}*/}
            {/*    nextView={setIsOpen}*/}
            {/*    open={isAddonsWithOtpOpen}*/}
            {/*    setOpen={() => true}*/}
            {/*    setOtpEmailSuccess={setOtpEmailSuccess}*/}
            {/*    setOtpSmsSuccess={setOtpSmsSuccess}*/}
            {/*/>*/}
        </Card>
    );
};
