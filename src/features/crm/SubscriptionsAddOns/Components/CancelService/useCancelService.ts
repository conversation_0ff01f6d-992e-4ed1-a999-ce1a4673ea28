import { useEffect, useState } from "react";
import { getAddOnsForService, getUniqueService } from "@services/subscription/accountId/api/getSubscriptionsAccountIds";
import { getInclusiveEquipments } from "@api-routes/equipments";
import { createSubscriptionTermination } from "@modules/accounts/apis/v2";

import {
    ICancelSubscriptionOrderPayload,
    IServiceToManage,
} from "@modules/wfeOrderFacade/interfaces/payloads/ICreateOrderPayload";
import { requestServiceTermination } from "@modules/wfeOrderFacade/apis/v0";
import { useTranslation } from "react-i18next";
import { TCreateOrderPayload as OrderStatusPayload } from "@modules/orderStatusFacade/interfaces/payloads";
import {
    EOrderStatusFacadeActivity,
    EOrderStatusFacadeBusinessStatus,
    EOrderStatusFacadeUseCase,
} from "@modules/orderStatusFacade/interfaces/constants";
import { createOrderVisit } from "@modules/orderStatusFacade/apis/v0";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { useUser } from "@hooks/useUser";

interface IAddOnResponse {
    serviceId: string;
    response: unknown;
    inclusiveEquipments?: unknown;
    error?: string;
}

export function useCancelService(
    selectedServiceIds: string[],
    appointmentConfirmation: boolean,
    setOrderIdForSchedule?: (orderId: string) => void,
    accountId: string,
    contactDetails: TContactDetails | undefined,
    onClearSelectedServiceIds?: () => void
) {
    const [addOnsData, setAddOnsData] = useState<IAddOnResponse[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [modalInfo, setModalInfo] = useState<{ open: boolean; message: string; success: boolean } | null>(null);
    const { t } = useTranslation(["order"]);
    const user = useUser();

    useEffect(() => {
        let isMounted = true;
        async function fetchAddOns() {
            setLoading(true);
            setError(null);
            const results: IAddOnResponse[] = [];
            for (const serviceId of selectedServiceIds) {
                try {
                    const response = await getAddOnsForService(String(serviceId));
                    const inclusiveEquipmentsResponse = await getInclusiveEquipments(String(serviceId));
                    results.push({ serviceId, response, inclusiveEquipments: inclusiveEquipmentsResponse });
                } catch (err) {
                    results.push({
                        serviceId,
                        response: null,
                        inclusiveEquipments: null,
                        error: err instanceof Error ? err.message : "Error",
                    });
                }
            }
            if (isMounted) {
                setAddOnsData(results);
                setLoading(false);
            }
        }
        if (selectedServiceIds.length > 0) {
            fetchAddOns();
        } else {
            setAddOnsData([]);
        }

        return () => {
            isMounted = false;
        };
    }, [selectedServiceIds]);

    async function executeSubscriptionTermination(serviceIds: string[]) {
        const now = new Date().toISOString();
        const successful: IServiceToManage[] = [];
        for (const serviceId of serviceIds) {
            const payload = {
                primaryReason: "Solicitud del cliente",
                scheduledAt: now,
                secondaryReason: "No se desea el servicio",
                source: "TELESALES",
            };
            try {
                const response = await createSubscriptionTermination(serviceId, payload);
                if (typeof response === "number" && response > 0) {
                    successful.push({ subscriptionId: Number(serviceId), requestId: response });
                }
            } catch (err: unknown) {
                if ((err as any)?.status === 409) {
                    // eslint-disable-next-line no-console
                    console.log(
                        `Ya existe una solicitud de cancelación para el servicio ${serviceId}:`,
                        (err as any)?.detail || err
                    );
                } else {
                    // eslint-disable-next-line no-console
                    console.error(`Error al cancelar el servicio ${serviceId}:`, err);
                }
            }
        }

        return successful;
    }

    async function executeWfeServiceTermination(
        serviceToManage: IServiceToManage[],
        orderId: string,
        appointmentConfirmed: boolean
    ) {
        const payload: ICancelSubscriptionOrderPayload = {
            serviceToManage,
            orderId,
            appointmentConfirmation: appointmentConfirmed,
            requestorUsername: user?.username,
            useCase: EOrderStatusFacadeUseCase.CANCEL_SUBSCRIPTION,
        };
        try {
            return await requestServiceTermination(payload);
        } catch (err) {
            // eslint-disable-next-line no-console
            console.error("Error in WFE Service Termination:", err);
            throw err;
        }
    }

    async function executeOrderVisit(orderId: string) {
        const createOrderPayload: OrderStatusPayload = {
            orderId: orderId,
            visitId: orderId,
            useCase: EOrderStatusFacadeUseCase.CANCEL_SUBSCRIPTION,
            accountId: accountId,
            clientId: contactDetails?.contactUuid ?? "",
            activity: EOrderStatusFacadeActivity.FRONTEND,
            businessStatus: EOrderStatusFacadeBusinessStatus.CANCEL_SUBSCRIPTION,
        };

        await createOrderVisit(createOrderPayload);
    }

    async function executeCancelService(showModal: boolean, appointmentConfirmed: boolean) {
        const { reference } = await getUniqueService();
        if (setOrderIdForSchedule) {
            setOrderIdForSchedule(reference);
        }

        if (appointmentConfirmed) {
            await executeOrderVisit(reference);
        }

        const successful = await executeSubscriptionTermination(selectedServiceIds);

        if (successful.length > 0) {
            const response = await executeWfeServiceTermination(successful, reference, appointmentConfirmed);
            if (showModal && (response.status === 200 || response.status === 201)) {
                setModalInfo({
                    open: true,
                    message: t("order:cancelRequestSent"),
                    success: true,
                });
            }
        }

        // Clear selectedServiceIds after successful execution
        if (onClearSelectedServiceIds) {
            onClearSelectedServiceIds();
        }
    }

    const closeModal = () => setModalInfo(null);

    return { addOnsData, loading, error, executeCancelService, modalInfo, closeModal };
}
