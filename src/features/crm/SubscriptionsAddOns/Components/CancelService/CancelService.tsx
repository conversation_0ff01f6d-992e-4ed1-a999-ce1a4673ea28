import React, { useState, useEffect } from "react";
import { useRef } from "react";
import { Tabs, Tab, Grid, Typography, Paper, Box, Button } from "@mui/material";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { useCancelService } from "./useCancelService";
import { IAddOns } from "@services/subscription/accountId/interface/IAddOns";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { DeliverInStoreDialog } from "@common/Dialog/CancelAddOnDialog/DeliverInStoreDialog";
import CancelConfirmationModal from "@features/crm/InformationSchedule/CancelConfirmationModal/CancelConfirmationModal";

interface IPropsMenuAddons {
    names: { name: string; label: string }[];
    accountId: string;
    addOnsServices: IAddOns | undefined;
    contactDetails: TContactDetails | undefined;
    setOrderIdForSchedule: React.Dispatch<React.SetStateAction<string>>;
    setShowCancellationService: React.Dispatch<React.SetStateAction<boolean>>;
    OrderIdForSchedule: string;
    handleShowSchedule: (show: boolean) => void;
    selectedServiceIds: string[];
    setSelectedServiceIds: React.Dispatch<React.SetStateAction<string[]>>;
    showDeliveredStoreCart: boolean;
    setShowDeliveredStoreCart: React.Dispatch<React.SetStateAction<boolean>>;
}

interface IAddonType {
    id: number;
    description: string;
    activatedAt?: string;
    itemGroupCode?: string;
    inclusiveEquipments?: { serialNumber?: string }[];
}

const TAB_TITLES = [
    { key: "TV_CHANNEL", label: "TV" },
    { key: "OTT", label: "Internet" },
    { key: "MATERIAL", label: "Material" },
    { key: "INCLUSIVE", label: "Inclusive" },
];

const CancelService = ({
    selectedServiceIds,
    setSelectedServiceIds,
    setShowCancellationService,
    handleShowSchedule,
    setShowDeliveredStoreCart,
    setOrderIdForSchedule,
    accountId,
    contactDetails,
    onMaterialAddonsChange,
}: IPropsMenuAddons & { onMaterialAddonsChange?: (addons: IAddonType[]) => void }) => {
    const { t } = useTranslation(["customer", "common"]);
    const [tabValue, setTabValue] = useState<string>(TAB_TITLES[0].key);
    const [selectedAddons] = useState<number[]>([]);
    const [isDeliverInStoreDialogOpen, setIsDeliverInStoreDialogOpen] = useState(false);
    const [deliverInStore, setDeliverInStore] = useState(false);
    const [isConfirming, setIsConfirming] = useState(false);
    const [appointmentConfirmation, setAppointmentConfirmation] = useState(false);
    const { addOnsData, loading, error, executeCancelService, modalInfo, closeModal } = useCancelService(
        selectedServiceIds,
        appointmentConfirmation,
        setOrderIdForSchedule,
        accountId,
        contactDetails,
        () => setSelectedServiceIds([])
    );

    const allAddOns: IAddonType[] = addOnsData
        .map((item) =>
            item.response && typeof item.response === "object" && "collection" in item.response
                ? (item.response as { collection: IAddonType[] }).collection
                : []
        )
        .flat();

    const tabAddOns: Record<string, IAddonType[]> = {
        TV_CHANNEL: allAddOns.filter((a) => a.itemGroupCode === "TV_CHANNEL"),
        OTT: allAddOns.filter((a) => a.itemGroupCode === "OTT"),
        MATERIAL: allAddOns.filter((a) => a.itemGroupCode === "MATERIAL"),
    };

    const lastMaterialAddonsRef = useRef<IAddonType[] | undefined>(undefined);

    // Para filtrar informacion de inclusiveEquipments
    const allInclusiveEquipments = addOnsData
        .map((item) =>
            item.inclusiveEquipments &&
            typeof item.inclusiveEquipments === "object" &&
            "collection" in item.inclusiveEquipments
                ? (
                      item.inclusiveEquipments as {
                          collection: {
                              description: string;
                              serialNumber: string;
                              equipmentId: number;
                              activatedAt: string;
                          }[];
                      }
                  ).collection
                : []
        )
        .flat();

    useEffect(() => {
        if (onMaterialAddonsChange) {
            const mappedInclusive = allInclusiveEquipments.map((eq) => ({
                id: eq.equipmentId,
                description: eq.description ?? "Inclusive Equipment",
                itemGroupCode: "INCLUSIVE",
                activatedAt: eq.activatedAt,
                inclusiveEquipments: [{ serialNumber: eq.serialNumber }],
            }));
            const combinedAddons = [...tabAddOns.MATERIAL, ...mappedInclusive];
            if (JSON.stringify(lastMaterialAddonsRef.current) !== JSON.stringify(combinedAddons)) {
                onMaterialAddonsChange(combinedAddons);
                lastMaterialAddonsRef.current = combinedAddons;
            }
        }
    }, [tabAddOns.MATERIAL, allInclusiveEquipments, onMaterialAddonsChange]);

    const handleTabChange = (_: React.SyntheticEvent, newValue: string) => {
        setTabValue(newValue);
    };

    const handleModalCloseDelivery = () => {
        setIsDeliverInStoreDialogOpen(false);
        setDeliverInStore(false);
    };

    const handleModalConfirmDelivery = () => {
        setIsConfirming(true);
        if (!deliverInStore) {
            setAppointmentConfirmation(true);
            handleShowSchedule(true);
            setShowCancellationService(false);
            executeCancelService(false, true);
        } else {
            setAppointmentConfirmation(false);
            setShowDeliveredStoreCart(true);
            setShowCancellationService(false);
            executeCancelService(false, false);
        }
        setIsConfirming(false);
        setIsDeliverInStoreDialogOpen(false);
    };

    const handleConfirmClick = () => {
        const hasMaterial = tabAddOns.MATERIAL && tabAddOns.MATERIAL.length > 0;
        const hasInclusive = allInclusiveEquipments && allInclusiveEquipments.length > 0;
        if (hasMaterial || hasInclusive) {
            setIsDeliverInStoreDialogOpen(true);
        } else {
            executeCancelService(true, false);
        }
    };

    const handleCancelClick = () => {
        setShowCancellationService(false);
        setSelectedServiceIds([]);
    };

    return (
        <div>
            {modalInfo?.open && (
                <CancelConfirmationModal
                    isOpen={modalInfo.open}
                    message={modalInfo.message}
                    success={modalInfo.success}
                    onClose={closeModal}
                />
            )}
            <h2>{t("customer:addOnsTitle")}</h2>
            <Tabs
                aria-label="Add-ons tabs"
                sx={{ mb: 2 }}
                value={tabValue}
                variant="fullWidth"
                onChange={handleTabChange}
            >
                {TAB_TITLES.map((tab) => (
                    <Tab key={tab.key} label={tab.label} value={tab.key} />
                ))}
            </Tabs>
            {loading ? (
                <Typography>Loading add-ons...</Typography>
            ) : error ? (
                <Typography color="error">Error: {error}</Typography>
            ) : tabValue === "INCLUSIVE" ? (
                allInclusiveEquipments.length > 0 ? (
                    <Grid container spacing={2}>
                        {allInclusiveEquipments.map((equipment, idx) => (
                            <Grid
                                item
                                key={idx}
                                sx={{
                                    mt: 1,
                                    mb: 1,
                                    p: 2,
                                    border: "1px solid #A0EAFF",
                                }}
                                xs={12}
                            >
                                <Typography fontWeight="bold" variant="subtitle2">
                                    {equipment.description}
                                </Typography>
                                <Typography variant="body2">
                                    Serial: {equipment.serialNumber || t("common:notAvailable")}
                                </Typography>
                            </Grid>
                        ))}
                    </Grid>
                ) : (
                    <Box
                        alignItems="center"
                        display="flex"
                        flexDirection="column"
                        justifyContent="center"
                        minHeight="250px"
                    >
                        <Paper
                            elevation={0}
                            sx={{
                                maxWidth: "500px",
                                width: "100%",
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                                justifyContent: "center",
                            }}
                        >
                            <InfoOutlinedIcon color="primary" sx={{ fontSize: 45 }} />
                            <Typography color="text.secondary" fontWeight="bold" mt={1} variant="h2">
                                {t("customer:noAddOnsAvailable")}
                            </Typography>
                        </Paper>
                    </Box>
                )
            ) : tabAddOns[tabValue].length > 0 ? (
                <Grid container spacing={2}>
                    {tabAddOns[tabValue].map((addon: IAddonType) => {
                        const isInCart = selectedAddons.includes(addon.id);
                        const isMaterial = String(addon.itemGroupCode ?? "").toLowerCase() === "material";
                        const serialNumber = isMaterial ? addon.inclusiveEquipments?.[0]?.serialNumber : undefined;

                        return (
                            <Grid
                                alignItems="center"
                                container
                                direction="row"
                                key={addon.id}
                                sx={{
                                    mt: 1,
                                    mb: 1,
                                    p: 2,
                                    border: "1px solid #A0EAFF",
                                    backgroundColor: isInCart ? "#0086ff" : "transparent",
                                }}
                            >
                                <Grid item xs={4}>
                                    <Typography
                                        fontWeight="bold"
                                        sx={{ color: isInCart ? "#ebebeb" : "#565656" }}
                                        variant="subtitle2"
                                    >
                                        {addon.description}
                                    </Typography>
                                    <Typography sx={{ color: isInCart ? "#ebebeb" : "text.secondary" }} variant="body2">
                                        ID: {addon.id}
                                    </Typography>
                                </Grid>
                                <Grid item xs={4}>
                                    <Typography sx={{ color: isInCart ? "#ebebeb" : "#565656" }} variant="body2">
                                        {t("customer:activationDate")}
                                    </Typography>
                                    <Typography sx={{ color: isInCart ? "#ebebeb" : "text.secondary" }} variant="body2">
                                        {addon.activatedAt ? dayjs(addon.activatedAt).format("DD/MM/YYYY") : "-"}
                                    </Typography>
                                </Grid>
                                <Grid item xs={4}>
                                    <Typography
                                        color="text.secondary"
                                        sx={{ color: isInCart ? "#ebebeb" : "#565656" }}
                                        variant="body2"
                                    >
                                        {t("customer:serial")}
                                    </Typography>
                                    <Typography sx={{ color: isInCart ? "#ebebeb" : "text.secondary" }} variant="body2">
                                        {isMaterial ? (serialNumber ? serialNumber : t("common:notAvailable")) : "N/A"}
                                    </Typography>
                                </Grid>
                                <Grid item xs={2}>
                                    {/* Botón de cancelación removido temporalmente */}
                                </Grid>
                            </Grid>
                        );
                    })}
                </Grid>
            ) : (
                <Box
                    alignItems="center"
                    display="flex"
                    flexDirection="column"
                    justifyContent="center"
                    minHeight="250px"
                >
                    <Paper
                        elevation={0}
                        sx={{
                            maxWidth: "500px",
                            width: "100%",
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <InfoOutlinedIcon color="primary" sx={{ fontSize: 45 }} />
                        <Typography color="text.secondary" fontWeight="bold" mt={1} variant="h2">
                            {t("customer:noAddOnsAvailable")}
                        </Typography>
                    </Paper>
                </Box>
            )}
            <Box sx={{ marginTop: 3, textAlign: "center" }}>
                <Grid container spacing={2}>
                    <Grid item xs={6}>
                        <Button
                            color="inherit"
                            fullWidth
                            variant="contained"
                            onClick={handleCancelClick}
                        >
                            {t("customer:cancelSubscriptionAddOns")}
                        </Button>
                    </Grid>
                    <Grid item xs={6}>
                        <Button fullWidth sx={{ mb: 2 }} variant="contained" onClick={handleConfirmClick}>
                            {t("customer:confirm")}
                        </Button>
                    </Grid>
                </Grid>
            </Box>
            <DeliverInStoreDialog
                deliverInStore={deliverInStore}
                handleModalCloseDelivery={handleModalCloseDelivery}
                handleModalConfirmDelivery={handleModalConfirmDelivery}
                isCancellingAddons={false}
                isConfirming={isConfirming}
                open={isDeliverInStoreDialogOpen}
                setDeliverInStore={setDeliverInStore}
            />
        </div>
    );
};

export default CancelService;
