import { useState, useEffect } from "react";
import { ISubscriptionsAccountIds } from "@services/subscription/accountId/interface/ISubscriptionsAccountIds";
import { getAddOnsForService } from "@services/subscription/accountId/api/getSubscriptionsAccountIds";
import { useTranslation } from "react-i18next";
import { EAddonItemStatus } from "@common/Addons/IAddonList";
import { generateBalance } from "@features/crm/AccountDetails/AccountDetailsHeader/Billing/BalanceAmount/useBalance";
import { IBalance } from "@features/crm/AccountDetails/AccountDetailsHeader/Billing/BalanceAmount/IBalance";

type AddOnItem = {
    status: string;
    code: string;
    description: string;
};

export function useSubscriptions(setShowCancellationService?: (show: boolean) => void) {
    const [subscriptions, setSubscriptions] = useState<ISubscriptionsAccountIds[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [modalInfo, setModalInfo] = useState<{ open: boolean; message: string; success: boolean } | null>(null);
    const { t } = useTranslation(["addon"]);

    useEffect(() => {
        setLoading(true);
        setTimeout(() => {
            try {
                setSubscriptions([]);
                setLoading(false);
            } catch (e) {
                setError("Failed to fetch subscriptions");
                setLoading(false);
            }
        }, 500);
    }, []);

    const cancelService = async (serviceId: number, _accountId: string) => {
        const hasOngoingProcess = await fetchAddOnsForService(serviceId);
        if (hasOngoingProcess) {
            return;
        }
        const paymentValid = await validatePayment(_accountId);
        if (paymentValid) {
            return;
        }

        setShowCancellationService(true);
    };

    const fetchAddOnsForService = async (serviceId: number): Promise<boolean> => {
        try {
            const response = await getAddOnsForService(String(serviceId));
            const ongoingTermination = response?.collection?.find(
                (item: AddOnItem) => item.status === EAddonItemStatus.ONGOING_TERMINATION
            );
            const ongoingAddition = response?.collection?.find(
                (item: AddOnItem) => item.status === EAddonItemStatus.ONGOING_ADDITION
            );
            if (ongoingTermination) {
                setModalInfo({
                    open: true,
                    message: t("addon:addonInTerminationProcess"),
                    success: false,
                });

                return true;
            } else if (ongoingAddition) {
                setModalInfo({
                    open: true,
                    message: t("addon:addonInAdditionProcess"),
                    success: false,
                });

                return true;
            }

            return false;
        } catch (err) {
            // eslint-disable-next-line no-console
            console.error("Error fetching AddOns:", err);

            return false;
        }
    };

    const validatePayment = async (accountId: string): Promise<boolean> => {
        try {
            const balance: IBalance = await generateBalance(accountId);
            if (
                balance &&
                typeof balance === "object" &&
                "totalAmount" in balance &&
                typeof balance.totalAmount === "number"
            ) {
                if (balance.totalAmount < 0) {
                    setModalInfo({
                        open: true,
                        message: t("addon:pendingPayment"),
                        success: false,
                    });

                    return true;
                }

                return false;
            }

            return false;
        } catch (err) {
            // eslint-disable-next-line no-console
            console.error("Error validating payment:", err);

            return false;
        }
    };

    const closeModal = () => setModalInfo(null);

    return { subscriptions, loading, error, cancelService, modalInfo, closeModal, validatePayment };
}
