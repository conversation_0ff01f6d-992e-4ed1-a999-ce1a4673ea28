import { STATUS_ADD_ONS, INTERNET_SERVICE_GROUP } from "@constants";
import { Box, Button, Grid, Typography } from "@mui/material";
import { ICatalogCharges } from "@services/subscription/accountId/interface/ICatalogs";
import {
    ISubscriptionsCollection,
    ISubscriptionsAccountIds,
} from "@services/subscription/accountId/interface/ISubscriptionsAccountIds";
import { useTranslation } from "react-i18next";
import { getConfig } from "@config";
import { useEffect, useState } from "react";
import { useSubscriptions } from "./Subscriptions/useSubscriptions";
import CancelConfirmationModal from "../../InformationSchedule/CancelConfirmationModal/CancelConfirmationModal";
import InfoIcon from "@mui/icons-material/Info";

interface IProps {
    subscriptions: ISubscriptionsAccountIds;
    classes: Record<string, string>;
    priceOfPlans: number[];
    index: number;
    catalogCharges: Map<string, ICatalogCharges>;
    accountId: string;
    setShowCancellationService: (show: boolean) => void;
    onAddServiceId: (serviceId: string) => void;
    serviceGroup: string;
    allSubscriptionIds?: string[];
    subscriptionsForAccountIds?: ISubscriptionsCollection;
}

export default function Subscriptions({
    subscriptions,
    classes,
    catalogCharges,
    accountId,
    setShowCancellationService,
    onAddServiceId,
    serviceGroup,
    allSubscriptionIds = [],
    subscriptionsForAccountIds, // Destructure new prop
}: IProps) {
    const { id, name, status, terminationStatus } = subscriptions;
    const { cartVatIncluded } = getConfig();
    const { t } = useTranslation(["customer", "common"]);
    const [myPrice, setMyPrice] = useState(0);
    const { cancelService, modalInfo, closeModal } = useSubscriptions(setShowCancellationService);

    useEffect(() => {
        subscriptions.charges.map((myCharge) => {
            if (catalogCharges.has(myCharge.catalogCode)) {
                const charge = catalogCharges.get(myCharge.catalogCode);
                if (charge) {
                    setMyPrice(
                        cartVatIncluded
                            ? charge.pricePlans[0].defaultPrice
                            : charge.pricePlans[0].defaultPriceVatExcluded
                    );
                }
            }
        });
    });

    return (
        <>
            {modalInfo?.open && (
                <CancelConfirmationModal
                    isOpen={modalInfo.open}
                    message={modalInfo.message}
                    success={modalInfo.success}
                    onClose={closeModal}
                />
            )}
            <Grid alignItems="center" container direction="row" key={id} spacing={2}>
                <Grid item xs={4}>
                    <Box className={classes.wrapSubscription}>
                        <Typography
                            className={
                                status === STATUS_ADD_ONS.ACTIVE
                                    ? classes.activeSubscriptionText
                                    : classes.inactiveSubscriptionText
                            }
                            variant="subtitle2"
                        >
                            {name}
                        </Typography>
                    </Box>
                </Grid>
                <Grid item xs={1}>
                    <Typography className={classes.subscriptionIdText}>{id}</Typography>
                </Grid>
                <Grid item xs={1}>
                    <Box textAlign="center">
                        <Typography className={classes.subscriptionLabelText}>{t("customer:price")}</Typography>
                        <Typography className={classes.subscriptionPriceText}>
                            {/*t("common:price", { price: priceOfPlans[index] })*/}
                            {t("common:price", { price: myPrice })}
                        </Typography>
                    </Box>
                </Grid>
                {terminationStatus === true ? (
                    <Grid alignItems="center" container direction="row" item spacing={0} xs={6}>
                        <Grid
                            item
                            sx={{
                                backgroundColor: "#fcfcfc",
                                color: "#1A237E",
                                padding: "7px 9px",
                                borderRadius: "3px",
                                borderColor: "#fcfcfc",
                                alignItems: "center",
                                justifyContent: "center",
                                display: "flex",
                                gap: "8px",
                                boxShadow: "0 2px 6px rgba(0, 0, 0, 0.08)",
                                fontSize: "14px",
                                width: "100%",
                            }}
                        >
                            <InfoIcon sx={{ fontSize: 20, color: "#1A237E" }} />
                            <Typography sx={{ fontWeight: 500, fontSize: "14px", color: "inherit" }} variant="body2">
                                {t("customer:ongoingCancellationAddon")}
                            </Typography>
                        </Grid>
                    </Grid>
                ) : (
                    <>
                        <Grid item xs={2}>
                            <Button
                                color="secondary"
                                fullWidth
                                sx={{ height: 34, fontSize: "14px" }}
                                variant="contained"
                            >
                                {t("customer:promotion")}
                            </Button>
                        </Grid>
                        <Grid item xs={2}>
                            <Button
                                color="inherit"
                                fullWidth
                                sx={{ height: 34, fontSize: "14px" }}
                                variant="contained"
                                onClick={() => {
                                    cancelService(id, accountId);
                                    if (serviceGroup === INTERNET_SERVICE_GROUP && allSubscriptionIds.length > 0) {
                                        Array.from(new Set(allSubscriptionIds)).forEach((subId) => {
                                            const found = subscriptionsForAccountIds?.collection?.find(
                                                (item: ISubscriptionsAccountIds) => String(item.id) === String(subId)
                                            );
                                            if (found && !found.terminationStatus) {
                                                onAddServiceId(subId);
                                            }
                                        });
                                    } else {
                                        onAddServiceId(String(id));
                                    }
                                }}
                            >
                                {t("customer:cancelSubscriptionAddOns")}
                            </Button>
                        </Grid>
                        <Grid item xs={2}>
                            <Button color="primary" fullWidth sx={{ height: 34, fontSize: "14px" }} variant="contained">
                                {t("customer:retention")}
                            </Button>
                        </Grid>
                    </>
                )}
            </Grid>
        </>
    );
}
