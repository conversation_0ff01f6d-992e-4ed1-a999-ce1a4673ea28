import React, { useState, useMemo } from "react";
import {
    Box,
    Grid,
    Typography,
    Paper,
} from "@mui/material";
import { OnOffSwitch } from "@common/OnOffSwitch/OnOffSwitch";
import { FormButton } from "@common/FormButton/FormButton";
import { ICollectionAddOns } from "@services/subscription/accountId/interface/IAddOns";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { useStyle } from "../../style";
import CancelOrderConfirmationModal from "@common/styleComponents/OrderConfirmationModal/CancelOrderConfirmationModal";
import { ConfirmEquipmentChangesDialog } from "@common/Dialog/CancelAddOnDialog/ConfirmEquipmentChangesDialog";


interface IAddonsDeliveredStoreCartProps {
    addedAddons: ICollectionAddOns[];
    isVisible: boolean;
    onConfirm: (updatedAddons: ICollectionAddOns[]) => Promise<boolean>;
    onCancel: () => void;
    onClose: () => void;
}

const AddonsDeliveredStoreCart: React.FC<IAddonsDeliveredStoreCartProps> = ({
    addedAddons,
    isVisible,
    onConfirm,
    onCancel,
    onClose,
}) => {
    const { t } = useTranslation(["common", "customer"]);
    const [showAskConfirmModal, setshowAskConfirmModal] = useState(false);
    const [showFinalConfirmModal, setshowFinalConfirmModal] = useState(false);
    const [addonStates, setAddonStates] = useState<Record<number, boolean>>({});
    const { classes } = useStyle();

    // Filtrar addons con itemGroupCode igual a 'MATERIAL' o 'INCLUSIVE'
    const materialAddons = useMemo(() => {
        if (!addedAddons) return [];

        return addedAddons.filter((addon) => ["MATERIAL", "INCLUSIVE"].includes(addon.itemGroupCode?.toUpperCase()));
    }, [addedAddons]);

    // Inicializar estados de los addons basado en recoveredEquipment
    React.useEffect(() => {
        const initialStates: Record<number, boolean> = {};
        materialAddons.forEach((addon) => {
            initialStates[addon.id] = addon.recoveredEquipment ?? false;
        });
        setAddonStates(initialStates);
    }, [materialAddons]);

    const handleToggleChange = (addonId: number, checked: boolean) => {
        setAddonStates(prev => ({
            ...prev,
            [addonId]: checked
        }));
    };

    const handleConfirmClick = () => {
        setshowAskConfirmModal(true);
    };

    const handleConfirmAction = async () => {
        // Crear addons actualizados con los nuevos valores de recoveredEquipment
        const updatedAddons = materialAddons.map(addon => ({
            ...addon,
            recoveredEquipment: addonStates[addon.id] ?? false
        }));
        const result = await onConfirm(updatedAddons);
        
        setshowAskConfirmModal(false);
        
        if(result){
            setshowFinalConfirmModal(true);
        }
    };

    const handleCancelModal = () => {
        setshowAskConfirmModal(false);
    };

    const handleCancelAction = () => {
        // Resetear estados a los valores originales
        const resetStates: Record<number, boolean> = {};
        materialAddons.forEach((addon) => {
            resetStates[addon.id] = addon.recoveredEquipment ?? false;
        });
        setAddonStates(resetStates);
        onCancel();
    };

    if (!isVisible) {
        return null;
    }

    return (
        <>
            <Box sx={{ pb: 1, pt: 2  }}>
                <Typography
                    className={`${classes.titleAddOn} ${classes.subscriptionTitleContainer}`}  sx={{ pb: 1 }}           
                >
                    {t("customer:addOnsTitle")}
                </Typography>
                <Typography
                    className={`${classes.titleAddOn} ${classes.subscriptionTitleContainer}`}  sx={{ pb: 1 }}           
                >
                    {t("customer:equipmentDeliveredInStoreTitle")}
                </Typography>
                        
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    {t("customer:selectEquipmentRecoveredMessage")}
                </Typography>

                {materialAddons.length === 0 ? (
                    <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                        {t("customer:noMaterialAddonsFound")}
                    </Typography>
                ) : (
                    <Grid container spacing={2}>
                        {materialAddons.map((addon) => (
                            <Grid item xs={12} key={addon.id}>
                                <Paper variant="outlined" sx={{ p: 2 }}>
                                    <Grid container alignItems="center" spacing={2}>
                                        <Grid item xs={3}>
                                            <Typography variant="body1" fontWeight="medium">
                                                {addon.description}
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary">
                                                ID: {addon.id}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={3}>
                                            <Typography variant="body2" sx={{ color: "#565656" }}>
                                                {t("customer:activationDate")}
                                            </Typography>
                                            <Typography variant="body2" sx={{ color: "text.secondary" }}>
                                                {addon.activatedAt ? dayjs(addon.activatedAt).format("DD/MM/YYYY") : "-"}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={3}>
                                            <Typography variant="body2" sx={{ color: "#565656" }}>
                                                {t("customer:serial")}
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary">
                                                {(() => {
                                                    const sn = addon.inclusiveEquipments?.[0]?.serialNumber;
                                                    const snText = sn == null ? "" : String(sn).trim();
                                                    return snText || t("common:notAvailable");
                                                })()}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={3} sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
                                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                                <OnOffSwitch
                                                    checked={addonStates[addon.id] ?? false}
                                                    handleChange={(checked) => handleToggleChange(addon.id, checked)}
                                                    inputProps={{ "aria-label": `Equipo recuperado ${addon.description}` }}
                                                />
                                                <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                                                    {addonStates[addon.id] ? t("common:delivered") : t("common:notDelivered")}
                                                </Typography>
                                            </Box>
                                        </Grid>
                                    </Grid>
                                </Paper>
                            </Grid>
                        ))}
                    </Grid>
                )}

                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3 }}>
                    <FormButton
                        buttonText={t("common:confirm")}
                        color="primary"
                        variant="contained"
                        onClick={handleConfirmClick}
                        disabled={materialAddons.length === 0}
                    />
                </Box>
            </Box>



            {/* Modal para preguntar si desea confirmar los cambios */}
            <ConfirmEquipmentChangesDialog
                handleCancelModal={handleCancelModal}
                handleConfirmAction={handleConfirmAction}
                open={showAskConfirmModal}
            />

            <CancelOrderConfirmationModal
                isOpen={showFinalConfirmModal}
                message={t("common:changesSavedSuccessfully")}
                onClose={onClose}
            />
        </>
    );
};

export default AddonsDeliveredStoreCart;