import { Card, CardContent, SvgIcon } from "@mui/material";
import { ReactComponent as History } from "@static/icons/history.svg";
import { NavigationTabs, SectionCard, useOutlinedCardStyle, useSectionIconStyle } from "@common";
import { useTranslation } from "react-i18next";
import { OrderHistoryList } from "./OrderHistoryList/OrderHistoryList";
// import { OrderHistoryList, OrderHistoryList as OrderHistoryListGT } from "./OrderHistoryList/gt/OrderHistoryListGT";
import { EOrderOrchestrator } from "@constants";
import { ReactComponent as ListIcon } from "@static/icons/list.svg";
import { ReactComponent as CalendarOutlinedIcon } from "@static/icons/calendar_outlined.svg";
import { useStyle } from "./style";
import { AppointmentList } from "@features/crm/Order/AppointmentList/AppointmentList";

interface IParams {
    accountId: string;
}

export const Order = (props: IParams) => {
    const { accountId } = props;
    const { t } = useTranslation("order");
    const { classes: outlinedCardClasses } = useOutlinedCardStyle();
    const { classes: iconClasses } = useSectionIconStyle();
    const { classes } = useStyle();

    const tabs = [
        // {
        //     component: <OrderHistoryListGT accountId={accountId} orderOrchestrator={EOrderOrchestrator.WFE} />,
        //     label: t("xwfe"),
        //     path: EOrderOrchestrator.XWFE,
        //     icon: <SvgIcon component={ListIcon} viewBox="2 3 23 23" />,
        // },
        {
            component: <OrderHistoryList accountId={accountId} orderOrchestrator={EOrderOrchestrator.WFE} />,
            label: t("wfe"),
            path: EOrderOrchestrator.WFE,
            icon: <SvgIcon component={ListIcon} viewBox="2 3 23 23" />,
        },
        {
            component: <AppointmentList accountId={accountId} />,
            label: t("appointments"),
            path: EOrderOrchestrator.APPOINTMENTS,
            icon: <SvgIcon component={CalendarOutlinedIcon} viewBox="0 0 23 23" />,
            // disable: true,
        },
    ];

    return (
        <Card className={outlinedCardClasses.main} elevation={0}>
            <CardContent>
                <SectionCard
                    icon={<SvgIcon className={iconClasses.main} component={History} viewBox="0 0 21 19" />}
                    title={t("orderHistory")}
                >
                    <NavigationTabs
                        centered
                        className={classes.tabs}
                        navTabList={tabs}
                        redirectUrl={EOrderOrchestrator.XWFE}
                    />
                </SectionCard>
            </CardContent>
        </Card>
    );
};
