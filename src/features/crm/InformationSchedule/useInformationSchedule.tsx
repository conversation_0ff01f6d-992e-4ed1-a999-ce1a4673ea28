import { useEffect, useState } from "react";
import { IAppointmentSchedule } from "../SubscriptionsAddOns/Components/SchedulesTable";
import { GetAppointmentsPaginated, IPaginatedAppointmentResponse } from "@common/Appointment/AppointmentAPI";
import { TContactDetails } from "../CustomerDetails/ICustomerDetails";
import { useTranslation } from "react-i18next";
// eslint-disable-next-line import/no-duplicates
import { format, parseISO } from "date-fns";
import { APPOINTMENT_DATE_FORMAT } from "@constants";
// eslint-disable-next-line import/no-duplicates
import { es } from "date-fns/locale";
import { useFetchState } from "@hooks/useFetchState";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { getUniqueService } from "@services/subscription/accountId/api/getSubscriptionsAccountIds";
import { getTask } from "@modules/tigoSalesFacade/apis/v1/field-service";
import { formatInTimeZone } from "date-fns-tz";

interface IUseInformationSchedule {
    contactDetails?: TContactDetails | undefined;
    accountId: string;
}

export const useInformationSchedule = ({ accountId, contactDetails }: IUseInformationSchedule) => {
    const [statusFilter, setStatusFilter] = useState<string[]>([]);
    const { t } = useTranslation(["customer", "appointment"]);
    const { endFetchingError } = useFetchState();
    // hooks for appointments
    const [appointments, setAppointments] = useState<IAppointmentSchedule[]>([]);
    const [addNewSchedule, setAddNewSchedule] = useState<boolean>(false);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [totalAppointments, setTotalAppointments] = useState(0);
    const [order, setOrder] = useState<"asc" | "desc">("desc");
    const [orderBy, setOrderBy] = useState("id");

    // hooks for schedule installation
    const [isSuccessCallId, setIsSuccessCallId] = useState<boolean>(false);
    const [callId, setCallId] = useState<string>("");
    const [availableSlots, setAvailableSlots] = useState<ISlot[]>();
    const [selectedSlot, setSelectedSlot] = useState<ISlot | undefined>(undefined);
    const [showNewScheledule, setShowNewSchedule] = useState<boolean>(false);

    const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
    const [tempSlotInfo, setTempSlotInfo] = useState<ReturnType<typeof procesarSlot> | null>(null);

    const handleSlotSelection = (slot: ISlot) => {
        const parsedSlot = procesarSlot(slot);
        setTempSlotInfo(parsedSlot);
        setSelectedSlot(slot);
        setIsConfirmModalOpen(true);
    };
    const handleCloseConfirmModal = () => {
        setTempSlotInfo(null);
        setIsConfirmModalOpen(false);
    };
    const handleConfirmAppointmentSlot = () => {
        setIsConfirmModalOpen(false);
    };
    const uniqueService = async () => {
        try {
            const response = await getUniqueService();
            setCallId(response.reference);
            setIsSuccessCallId(true);
        } catch (error) {
            endFetchingError(error);
        }
    };
    const procesarSlot = (slot: ISlot) => {
        const titulo = format(parseISO(slot.start), APPOINTMENT_DATE_FORMAT, { locale: es });
        const startHour = parseISO(slot.start).getUTCHours();
        const horaInicio = formatInTimeZone(slot.start,'UTC','h:mm',{ locale: es });
        const horaFin = formatInTimeZone(slot.finish,'UTC','h:mm',{ locale: es });

        const time_slot = startHour === 8 ? "AM" : "PM";
        const jornada = startHour === 8 ? t("appointment:morning") : t("appointment:afternoon");
        const horario = horaInicio + " - " + horaFin;

        return {
            titulo,
            jornada,
            horario,
            time_slot,
        };
    };

    useEffect(() => {
        const uniqueCallId = localStorage.getItem("UNIQUE_CALL_ID");
        if (!isSuccessCallId && !uniqueCallId) {
            uniqueService();
        }

        if (uniqueCallId) {
            setCallId(uniqueCallId);
        }
    }, [isSuccessCallId]);

    useEffect(() => {
        if (callId) {
            localStorage.setItem("UNIQUE_CALL_ID", callId);
        }
    }, [callId]);

    const fetchAppointments = async () => {
        try {
            const sort = `${orderBy},${order}`;
            const statusParam = statusFilter.length > 0 ? statusFilter : undefined;
            const data: IPaginatedAppointmentResponse = await GetAppointmentsPaginated(
                contactDetails?.contactUuid ?? accountId,
                page,
                rowsPerPage,
                sort,
                statusParam
            );

            if (data.status === 200 && data.data) {
                const initialAppointments = data.data.content.map((item: any) => ({
                    id: item.id?.toString() ?? "",
                    at: item.at ?? "",
                    reason: item.reason ?? "",
                    callId: item.correlationId ?? "",
                    correlationId: item.correlationId ?? "",
                    status: item.status ?? "",
                    addressId: item.addressId?.toString() ?? "",
                    taskType: item.taskType ?? "",
                    area: item.area ?? "",
                }));
                setAppointments(initialAppointments);
                setTotalAppointments(data.data.totalElements);
            }
        } catch (error) {
            throw new Error("Error al obtener el appointment");
        }
    };

    const fetchTaskForAppointment = async (appointment: IAppointmentSchedule) => {
        const callId = appointment.correlationId || appointment.callId;
        if (callId) {
            try {
                const taskInfo = await getTask({ callId });
                return taskInfo;
            } catch (error) {
                console.error('Error al obtener la task:', error);
                return null;
            }
        }
        return null;
    };

    useEffect(() => {
        fetchAppointments();
    }, [accountId, page, rowsPerPage, order, orderBy, statusFilter]);

    const handleChangePage = (_event: unknown, newPage: number) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleRequestSort = (_event: React.MouseEvent<unknown>, property: string) => {
        const isAsc = orderBy === property && order === "asc";
        setOrder(isAsc ? "desc" : "asc");
        setOrderBy(property);
    };

    return {
        appointments,
        setAddNewSchedule,
        addNewSchedule,
        procesarSlot,
        callId,
        availableSlots,
        setAvailableSlots,
        selectedSlot,
        setSelectedSlot,
        setShowNewSchedule,
        showNewScheledule,

        handleSlotSelection,
        fetchTaskForAppointment,
        isConfirmModalOpen,
        handleCloseConfirmModal,
        handleConfirmAppointmentSlot,
        tempSlotInfo,

        fetchAppointments,
        page,
        rowsPerPage,
        totalAppointments,
        handleChangePage,
        handleChangeRowsPerPage,
        order,
        orderBy,
        handleRequestSort,
        statusFilter,
        setStatusFilter,
    };
};
