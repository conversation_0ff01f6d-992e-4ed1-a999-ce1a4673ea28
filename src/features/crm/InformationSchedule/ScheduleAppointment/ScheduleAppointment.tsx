import { IAddress } from "@common/Address/IAddress";
import ScheduleForm from "@common/ScheduleForm/ScheduleForm";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { <PERSON><PERSON>, Divider, Grid, TextField } from "@mui/material";
import { Dispatch, useState } from "react";
import { useScheduleAppointment } from "./useScheduleAppointment";
import { useFetchState } from "@hooks/useFetchState";
import { ConfirmationModal, useSnackBar } from "@common";
import ReScheduleConfirmationModal from "@common/styleComponents/OrderConfirmationModal/ReScheduleConfirmationModal";
import { processTaskEx } from "@modules/tigoSalesFacade/apis/v1/field-service";
import { FieldServiceConfirmAppointment } from "@modules/tigoSalesFacade/interfaces/payloads/ITigoFieldServiceConfirmAppointment";
import { useTranslation } from "react-i18next";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { CreateAppointment, IAppointmentRequest } from "@common/Appointment/AppointmentAPI";
import { MCCRM_COMMENT } from "@constants";

interface IScheduleApppointmentProps {
    accountId: string;
    availableSlots: ISlot[] | undefined;
    callId: string;
    filterContact: IAddress | undefined;
    filterAddress: IAddress | undefined;
    contactDetails?: TContactDetails | undefined;
    procesarSlot: (slot: ISlot) => {
        titulo: string;
        jornada: string;
        horario: string;
        time_slot: string;
    };
    selectedSlot: ISlot | undefined;
    setAvailableSlots: Dispatch<React.SetStateAction<ISlot[] | undefined>>;
    setSelectedSlot: (slot: ISlot) => void;
    setAddNewSchedule: (value: boolean) => void;
    setShowNewSchedule: (value: boolean) => void;
    fetchAppointments: () => Promise<void>;
}

const TASK_TYPE_NEW_SCHEDULE = process.env.TASK_TYPE_NEW_SCHEDULE || "";
const ENVIRONMENT_TIME_INTERVAL_NUMBER = process.env.ENVIRONMENT_TIME_INTERVAL_NUMBER || "";
const ENVIRONMENT_TIME_INTERVAL_AREA = process.env.ENVIRONMENT_TIME_INTERVAL_AREA || "";
const ENVIRONMENT_TIME_INTERVAL_DISTRICT = process.env.ENVIRONMENT_TIME_INTERVAL_DISTRICT || "";
const ENVIRONMENT_TIME_INTERVAL_PRIORITY = process.env.ENVIRONMENT_TIME_INTERVAL_PRIORITY || "";
const ENVIRONMENT_TIME_INTERVAL_COUNTRYID = process.env.ENVIRONMENT_TIME_INTERVAL_COUNTRYID || "";
const ENVIRONMENT_TIME_INTERVAL_CITY = process.env.ENVIRONMENT_TIME_INTERVAL_CITY || "";


const ScheduleAppointment = ({
    accountId,
    availableSlots,
    callId,
    filterContact,
    procesarSlot,
    selectedSlot,
    setAvailableSlots,
    setSelectedSlot,
    setAddNewSchedule,
    setShowNewSchedule,
    contactDetails,
    filterAddress,
    fetchAppointments
}: IScheduleApppointmentProps) => {
    const { t } = useTranslation(["acquisition", "appointment", "address", "common", "customer"]);
    const { verifyUserCase, userIsVerified } = useScheduleAppointment();
    const [latitude, longitude] = (filterAddress?.streetNumber ?? "").split("**");

    const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
    const [tempSlotInfo, setTempSlotInfo] = useState<ReturnType<typeof procesarSlot> | null>(null);
    const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);

    const { setSnackBarSuccess, setSnackBarError } = useSnackBar();
    const { startFetching, endFetching, endFetchingError } = useFetchState();

    const handleSlotSelection = (slot: ISlot) => {
        setTempSlotInfo(procesarSlot(slot));
        setSelectedSlot(slot);
        setIsConfirmModalOpen(true);
    };

    const handleBackToTable = () => {
    setIsSuccessModalOpen(false);
    setAddNewSchedule(false);   
    setShowNewSchedule(false);  
    setAvailableSlots([]);
    fetchAppointments();
};

    const handleCloseConfirmModal = () => {
        setIsConfirmModalOpen(false);
        setTempSlotInfo(null);
    };

    function getTimeSlotCode(dateStr?: string): "AM" | "PM" | "" {
        if (!dateStr) return "";
        const hour = new Date(dateStr).getUTCHours();
        return hour === 8 ? "AM" : "PM";
    }

    const handleConfirmSlot = async () => {
        if (!selectedSlot?.start || !selectedSlot?.finish) return;

        setIsConfirmModalOpen(false);
        try {
            debugger
            startFetching();
            const payload: FieldServiceConfirmAppointment = {
                callId,
                earlyStart: selectedSlot.start,
                lateStart: selectedSlot.finish,
                appointmentStart: selectedSlot.start,
                appointmentFinish: selectedSlot.finish,
                taskType: TASK_TYPE_NEW_SCHEDULE,
                dueDate: selectedSlot?.finish,
                district: filterAddress?.poBox ?? ENVIRONMENT_TIME_INTERVAL_DISTRICT,
                countryId: ENVIRONMENT_TIME_INTERVAL_COUNTRYID,
                area: ENVIRONMENT_TIME_INTERVAL_AREA,
                number: Number(ENVIRONMENT_TIME_INTERVAL_NUMBER),
                priority: Number(ENVIRONMENT_TIME_INTERVAL_PRIORITY),
                city: ENVIRONMENT_TIME_INTERVAL_CITY,
                street: filterContact?.street ?? "",
                mcBillingAccountInfo: contactDetails?.contactAddresses?.[0]?.addressLine1 ?? "",
                mcContactEmail: contactDetails?.emails?.[0]?.email ?? "",
                mcCustomerCode: contactDetails?.identityDocuments?.[0]?.identifier ?? "",
                mcCustomerPhoneNumber: contactDetails?.phoneNumbers?.[0]?.phoneNumber
                    ? Number(contactDetails.phoneNumbers[0].phoneNumber)
                    : undefined,
                latitude: latitude || "",
                longitude: longitude || "",
                mcCustomerIdentityNumber: contactDetails?.identityDocuments?.[0]?.identifier ?? "",
                mcCustomerClass: "Residencial",
                mcOpeningReason: "",
                //customer: `[${contactDetails?.identityDocuments?.[0]?.identifier ?? ""}] ${contactDetails?.person?.firstName ?? ""}`,
                customer: `[${accountId ?? ""}] ${contactDetails?.person?.firstName ?? ""} ${contactDetails?.person?.lastName ?? ""}`,
                tdRequired: true,
                mcCrmComment: MCCRM_COMMENT,
                mcConnectionData: (filterContact?.addressLine1 ?? "") + (filterContact?.street ?? ""),
                contactName: contactDetails?.person?.firstName ?? "",
                contactPhoneNumber: contactDetails?.phoneNumbers?.[0]?.phoneNumber
                    ? Number(contactDetails.phoneNumbers[0].phoneNumber)
                    : undefined,
            };
            
            const response = await processTaskEx(payload);

            if (response?.messageError && response.messageError !== "") {
                setSnackBarError(`DENEGADO: ${response.messageError}`);
                endFetching();
                return;
            }

            //SE COMENTA HASTA QUE SE DEFINA EL BIEN EL FLUJO DE WFE EN ESTE PROCESO DE AGENDAMIENTO
            // const payloadWFE: FieldServiceConfirmAppointmentWFE = {
            //    addressId: filterContact?.addressLine1 ?? "",
            //   at: selectedSlot?.start,
            //    contactUuid: contactDetails?.contactUuid ?? "",
            //    correlationId: callId,
            //    duration: 30,
            //   reasonCode: "INSTALACION_HFC",
            //    source: "TIGO_WEB",
            //    timeSlotCode: selectedSlot?.start ?? "",
            //  };
            const appointmentRequest: IAppointmentRequest = {
                addressId: filterContact?.id ?? 0,
                at: selectedSlot?.start ?? "",
                contactUuid: contactDetails?.contactUuid ?? "",
                correlationId: callId,
                duration: 240,
                timeSlotCode: getTimeSlotCode(selectedSlot?.start),
                source: "FIELD-SERVICE",
                reasonCode: "FIX"
            };

            try {
                await CreateAppointment(appointmentRequest);
            } catch (createError: any) {
                const errorMsg = createError?.errorMessage || createError?.detail || createError?.message || t("appointment:confirmError");
                setSnackBarError(errorMsg);
                endFetching();
                return;
            }

            //await processTasKWFE(callId, payloadWFE).catch((error) => {
            //     console.warn("Proceso WFE fallido:", error);
            // });

            setSnackBarSuccess(t("appointment:confirmSuccess"));
            endFetching();
            setAddNewSchedule(false);
            setShowNewSchedule(true);
            setIsSuccessModalOpen(true);
            setIsSuccessModalOpen(true);
            endFetching();
        } catch (err: any) {
            endFetchingError(err);
            setSnackBarError(`${t("appointment:confirmError")}: ${err.message}`);
        }
    };
    
    return (
        <>
            <Grid container direction={"row"} spacing={2} width={"100%"}>
                <Grid item xs={12}>
                    <Divider textAlign="center">{t("customer:numberOfCase")}</Divider>
                </Grid>
                <Grid item xs={4}>
                    <TextField fullWidth label={"No. de Caso"} />
                </Grid>
                <Grid item xs={2}>
                    <Button color="primary" variant="outlined" onClick={() => verifyUserCase("222")}>
                        {t("common:verify")}
                    </Button>
                </Grid>

                {userIsVerified?.status?.name === "open" && (
                    <Grid item xs={12}>
                        <ScheduleForm
                            availableSlots={availableSlots}
                            callId={callId ?? ""}
                            fullWidth
                            municipalityStr={filterContact?.street ?? ""}
                            districtStr={filterContact?.town ?? ""}
                            townshipStr={filterContact?.addressLine1 ?? ""}
                            neighborhoodStr={filterContact?.area ?? ""}
                            streetStr={filterContact?.street ?? ""}
                            houseStr={filterContact?.addressLine3 ?? ""}
                            provinceStr={filterContact?.addressLine2 ?? ""}
                            procesarSlot={procesarSlot}
                            selectedSlot={selectedSlot}
                            setAvailableSlots={setAvailableSlots}
                            setSelectedSlot={selectedSlot}
                            handleSlotSelection={handleSlotSelection}
                            modalTitle={t("appointment:confirmTitle")}
                            isOpen={isConfirmModalOpen}
                            setIsOpen={setIsConfirmModalOpen}
                            handleConfirmAppointment={handleConfirmSlot}
                            taskType={TASK_TYPE_NEW_SCHEDULE}
                            districtOverride={filterAddress?.poBox ?? ENVIRONMENT_TIME_INTERVAL_DISTRICT}
                            fromReschedule={true}
                            latitudeOverride={latitude}
                            longitudeOverride={longitude}
                        />
                        <Button
                            color="secondary"
                            variant="contained"
                            onClick={() => {
                                setAddNewSchedule(false);
                                setShowNewSchedule(false);
                            }}
                        >
                            {t("common:cancel")}
                        </Button>
                    </Grid>
                )}
            </Grid>

            <ConfirmationModal
                open={isConfirmModalOpen}
                title={t("appointment:confirmTitle")}
                handleClose={handleCloseConfirmModal}
                handleConfirm={handleConfirmSlot}
            >
                <p>{t("appointment:confirmMessage")}</p>
                {tempSlotInfo && (
                    <Grid container marginTop={2} spacing={2}>
                        <Grid item md={6} xs={12}>
                            <TextField
                                disabled
                                fullWidth
                                label={t("appointment:detailAppointment.date")}
                                value={tempSlotInfo.titulo}
                            />
                        </Grid>
                        <Grid item md={6} xs={12}>
                            <TextField
                                disabled
                                fullWidth
                                label={tempSlotInfo.jornada}
                                value={tempSlotInfo.horario}
                            />
                        </Grid>
                    </Grid>
                )}
            </ConfirmationModal>
            <ReScheduleConfirmationModal
                isOpen={isSuccessModalOpen}
                message={t("common:scheduleSuccesfullMessage", { returnObjects: false })}
                onClose={handleBackToTable}
            />
        </>
    );
};
export default ScheduleAppointment;
