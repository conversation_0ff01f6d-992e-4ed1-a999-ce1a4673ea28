import {
    PrivateV1EndpointOptions,
    getMicroserviceEndpoint,
    PrivateV2EndpointOptions,
    getGatewayEndpoint,
} from "@api-routes/endpoints";
import { EServiceDomain } from "@pages/crm/Customer/SubscriptionDetails/IServiceDetails";
import { IFetchOptions } from "itsf-ui-common";
import { fetcher } from "@utils/fetcher";
import { IAddOns } from "@services/subscription/accountId/interface/IAddOns";

const EQUIPMENTS_ENDPOINT = "equipments/";

const EQUIPMENTS_AUTHENTICATED_ENDPOINT_V1 = getMicroserviceEndpoint(EQUIPMENTS_ENDPOINT, PrivateV1EndpointOptions);

const EQUIPMENTS_AUTHENTICATED_ENDPOINT_V2 = getMicroserviceEndpoint(EQUIPMENTS_ENDPOINT, PrivateV2EndpointOptions);

const simCardsAuthenticatedBaseApiRoute = () => `${EQUIPMENTS_AUTHENTICATED_ENDPOINT_V1}sim-cards`;

export const replacementSimCardRequestApiRoute = (serviceId: string) =>
    `${simCardsAuthenticatedBaseApiRoute()}-requests?serviceId=${serviceId}`;

export const simSwapChargesApiRoute = (serviceId: string) =>
    `${simCardsAuthenticatedBaseApiRoute()}/charges?serviceId=${serviceId}`;

export const swapSimApiRoute = () => `${simCardsAuthenticatedBaseApiRoute()}/swap`;

export const orderNewSimApiRoute = () => `${simCardsAuthenticatedBaseApiRoute()}/order`;

export const activateSimApiRoute = () => `${simCardsAuthenticatedBaseApiRoute()}/activate`;

export const equipmentReasonListApiRoute = (serviceDomain: EServiceDomain) =>
    `${EQUIPMENTS_AUTHENTICATED_ENDPOINT_V2}equipments/replacements/reasons?serviceDomain=${serviceDomain}`;

export const getInclusiveEquipments = (serviceId: string, options: IFetchOptions = {}) => {
    return fetcher<IAddOns>(`${getGatewayEndpoint()}${EQUIPMENTS_ENDPOINT}api/v2/services/${serviceId}/equipments`, {
        method: "GET",
        ...options,
    });
};
