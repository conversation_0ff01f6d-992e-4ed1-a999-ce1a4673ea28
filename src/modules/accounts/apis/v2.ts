import { IFetchOptions } from "itsf-ui-common";
import { fetcher } from "@utils/fetcher";
import { getGatewayEndpoint, getMicroserviceEndpoint, PrivateV2EndpointOptions } from "@api-routes/endpoints";
import { mapTerminationRequestsResponseListToPendingTermination } from "@modules/accounts/mappers/mapTerminationRequestResponse";

import { ITerminationRequest } from "@modules/accounts/interfaces/models/ITerminationRequest";
import { ISubscriptionTermination } from "@services/subscription/accountId/interface/IAppointment";

const SUBSCRIPTION_MANAGEMENT_ENDPOINT = "subscription-management/";
const SUBSCRIPTION_MANAGEMENT_AUTHENTICATED_ENDPOINT_V2 = getMicroserviceEndpoint(
    SUBSCRIPTION_MANAGEMENT_ENDPOINT,
    PrivateV2EndpointOptions
);

export const getPendingTerminationRequest = (subscriptionId: string, options: IFetchOptions = {}) =>
    fetcher<ITerminationRequest>(
        `${SUBSCRIPTION_MANAGEMENT_AUTHENTICATED_ENDPOINT_V2}subscriptions/${subscriptionId}/terminations`,
        {
            mapper: mapTerminationRequestsResponseListToPendingTermination,
            ...options,
        }
    );

export const createSubscriptionTermination = (serviceId: string, data: ISubscriptionTermination) => {
    return fetcher<string | number>(
        `${getGatewayEndpoint()}/accounts/private/subscriptions/${serviceId}/terminations`,
        {
            method: "POST",
            body: { ...data },
        }
    );
};
