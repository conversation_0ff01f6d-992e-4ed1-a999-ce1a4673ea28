import { getMicroserviceEndpoint, PrivateV1EndpointOptions } from "@api-routes/endpoints";
import { IFetchOptions } from "itsf-ui-common";
import { fetcher } from "@utils/fetcher";
import { ICreateAppointmentPayload, IUpdateAppointmentPayload } from "@modules/appointments/interfaces";
import { IAppointmentListPageResponse } from "@modules/appointments/interfaces/responses/IAppointmentListPageResponse";
import { IAppointmentNotePayload } from "@modules/appointments/interfaces/payloads/IAppointmentNotePayload";
import { mapAppointmentDate } from "@modules/appointments/mappers/mapAppointmentDate";

const APPOINTMENTS_ENDPOINT = "appointments/";
const APPOINTMENTS_AUTHENTICATED_ENDPOINT = getMicroserviceEndpoint(APPOINTMENTS_ENDPOINT, PrivateV1EndpointOptions);

export const appointmentsBaseApiRoute = () => `${APPOINTMENTS_AUTHENTICATED_ENDPOINT}appointments`;
// export const appointmentsBaseApiRoute = () =>
    // `https://glx-crm-pa-stg.tigo.cam/api-gateway/appointments/api/v1/private/auth/contact`;

export const postAppointment = (payload: ICreateAppointmentPayload, options: IFetchOptions = {}) =>
    fetcher<string>(`${APPOINTMENTS_AUTHENTICATED_ENDPOINT}appointments`, {
        method: "POST",
        body: payload,
        ...options,
    });

export const updateAppointment = (
    appointmentId: string,
    payload: IUpdateAppointmentPayload,
    options: IFetchOptions = {}
) =>
    fetcher<IAppointmentListPageResponse>(`${appointmentsBaseApiRoute()}?id=${appointmentId}`, {
        method: "PATCH",
        body: payload,
        ...options,
    });

export const postAppointmentNote = (
    appointmentId: string,
    payload: IAppointmentNotePayload,
    options: IFetchOptions = {}
) =>
    fetcher<boolean>(`${appointmentsBaseApiRoute()}/${appointmentId}/notes`, {
        method: "POST",
        body: payload,
        forwardError: true,
        ...options,
    });

export const getAppointments = (correlationId: string, options: IFetchOptions = {}) => {
    return fetcher<string>(`${appointmentsBaseApiRoute()}?correlationId=${correlationId}`, {
        mapper: mapAppointmentDate,
        ...options,
    });
};
