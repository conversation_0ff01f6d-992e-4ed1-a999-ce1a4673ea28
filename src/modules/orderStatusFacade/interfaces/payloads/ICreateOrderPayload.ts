import {
    EOrderStatusFacadeUseCase,
    EOrderStatusFacadeBusinessStatus,
    EOrderStatusFacadeActivity,
} from "@modules/orderStatusFacade/interfaces/constants";

export type TCreateOrderPayload =
    | ICreateOrderFakePayload;


interface ICreateOrderFakePayload {
    orderId: string;
    visitId: string;
    useCase: EOrderStatusFacadeUseCase.REMOVE_ADDONS | EOrderStatusFacadeUseCase.CANCEL_SUBSCRIPTION;
    accountId: string;
    clientId: string;
    activity: EOrderStatusFacadeActivity.FRONTEND;
    businessStatus:
        | EOrderStatusFacadeBusinessStatus.REMOVE_ADDONS
        | EOrderStatusFacadeBusinessStatus.CANCEL_SUBSCRIPTION;
}
