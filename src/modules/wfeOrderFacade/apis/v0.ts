import { getGatewayEndpoint, getMicroserviceEndpoint, InternalV1EndpointOptions } from "@api-routes/endpoints";
import { fetcher } from "@utils/fetcher";
import { IFetchOptions } from "itsf-ui-common";
import { ICancelSubscriptionOrderPayload, TCreateOrderPayload } from "../interfaces/payloads";
import { IWfeOrderFacadeCreateOrderResponse } from "../interfaces/responses/IWFEOrderFacadeCreateOrderResponse";

const WFE_ENDPOINT = "wfe-order-facade/";
const WFE_PRIVATE_ENDPOINT = getMicroserviceEndpoint(WFE_ENDPOINT, InternalV1EndpointOptions);

export const cancelOrder = (orderId: string, options: IFetchOptions = {}) =>
    fetcher<void>(`${WFE_PRIVATE_ENDPOINT}orders/${orderId}`, {
        method: "DELETE",
        ...options,
    });

export const createOrder = (payload: TCreateOrderPayload, isSync = true, options: IFetchOptions = {}) => {
    const url = new URL(`${WFE_PRIVATE_ENDPOINT}orders`);
    url.searchParams.append("isSync", String(isSync));

    return fetcher<IWfeOrderFacadeCreateOrderResponse>(url.toString(), {
        method: "POST",
        body: payload,
        ...options,
    });
};

export const requestServiceTermination = async (data: ICancelSubscriptionOrderPayload) => {
    const response = await fetch(`${getGatewayEndpoint()}${WFE_ENDPOINT}api/v1/orders?isSync=true`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({ ...data }),
    });
    const body = await response.json().catch(() => null);

    return { status: response.status, body };
};
