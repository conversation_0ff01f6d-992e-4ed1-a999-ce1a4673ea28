{"name": "tigo-b2c-crm-ui", "version": "1.1.2-10969", "sideEffects": false, "dockerRegistrySubpath": "galaxion", "publishConfig": {"registry": "https://nexus-tsfcore.tigo.cam/repository/npm-public/"}, "engines": {"node": "16.14.2"}, "scripts": {"build": "vite build --mode=production", "build:dev": "vite build --mode=development", "build:devgt": "vite build --mode=development_gt", "build:devpa": "vite build --mode=development_pa", "build:analyze": "ANALYZE=true vite build --mode=production", "start:dev": "vite", "start:devgt": "vite --mode=development_gt", "start:devpa": "vite --mode=development_pa", "test": "node --max_old_space_size=8192 --expose-gc ./node_modules/.bin/jest --coverage -w=4 --silent --ci --logHeapUsage", "test:dev": "jest", "test:watch": "jest --watch", "lint": "eslint --quiet --ext .ts,.tsx src/", "lint:dev": "eslint --cache --fix --ext .ts,.tsx src/", "prettier:check": "prettier --check .", "prettier:dev": "prettier --write .", "compile": "npx tsc --noEmit", "prepare": "husky install", "generateChangelog": "changelog", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@emotion/react": "11.10.5", "@emotion/styled": "11.10.5", "@mui/base": "5.0.0-alpha.101", "@mui/icons-material": "5.4.1", "@mui/material": "^5.10.3", "@mui/x-date-pickers": "5.0.4", "@originjs/vite-plugin-federation": "^1.3.6", "core-js": "3.27.1", "date-fns": "2.29.3", "date-fns-tz": "1.3.7", "dayjs": "^1.11.11", "deepmerge": "4.2.2", "embla-carousel-react": "6.2.0", "file-saver": "2.0.5", "i18next": "21.6.10", "itsf-ui-common": "6.1.5", "react": "18.2.0", "react-dom": "18.2.0", "react-helmet": "6.1.0", "react-hook-form": "7.27.0", "react-i18next": "11.15.3", "react-json-view-lite": "0.9.4", "react-router-dom": "5.3.0", "swr": "1.2.2", "tigo-b2c-crm-ui": "file:", "uuid": "^9.0.1", "zustand": "4.5.1"}, "devDependencies": {"@babel/core": "7.20.12", "@babel/preset-env": "7.20.2", "@babel/preset-react": "7.18.6", "@babel/preset-typescript": "7.18.6", "@itsf/changelog": "1.3.0", "@itsf/eslint-config": "1.5.2-alpha.2", "@storybook/addon-essentials": "7.5.2", "@storybook/addon-interactions": "7.5.2", "@storybook/addon-links": "7.5.2", "@storybook/addon-onboarding": "1.0.8", "@storybook/addon-styling": "1.3.7", "@storybook/blocks": "7.5.2", "@storybook/react": "7.5.2", "@storybook/react-vite": "7.5.2", "@storybook/testing-library": "0.2.2", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "13.4.0", "@types/file-saver": "2.0.5", "@types/jest": "29.4.0", "@types/node": "17.0.16", "@types/react": "18.0.27", "@types/react-dom": "18.0.10", "@types/react-helmet": "6.1.6", "@types/react-router-dom": "5.3.3", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "5.12.0", "@typescript-eslint/parser": "5.12.0", "@vitejs/plugin-react": "3.0.1", "babel-plugin-import": "1.13.8", "babel-plugin-transform-class-properties": "6.24.1", "eslint": "7.29.0", "eslint-config-prettier": "8.3.0", "eslint-import-resolver-jest": "3.0.0", "eslint-import-resolver-typescript": "3.5.2", "eslint-plugin-import": "2.25.4", "eslint-plugin-itsf": "0.3.7", "eslint-plugin-jsx-a11y": "6.5.1", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-react": "7.32.1", "eslint-plugin-react-hooks": "4.3.0", "eslint-plugin-storybook": "0.6.15", "eslint-plugin-testing-library": "5.10.0", "fetch-mock": "9.11.0", "husky": "8.0.3", "jest": "29.4.0", "jest-environment-jsdom": "29.4.0", "node-fetch": "2.6.7", "prettier": "2.8.2", "rollup-plugin-visualizer": "5.5.4", "tss-react": "4.4.4", "typescript": "4.5.5", "vite": "4.0.5", "vite-plugin-html": "3.2.0", "vite-plugin-svgr": "2.4.0"}}